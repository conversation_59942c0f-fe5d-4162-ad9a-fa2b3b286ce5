/*
  Localizable.strings (Spanish)
  DIYNoise
*/

// MARK: - App Name & Main
"app_name" = "DIY Noise";
"app_subtitle" = "Crea tu ruido blanco personalizado";

// MARK: - Tab Bar
"tab_sounds" = "Sonidos";
"tab_favorites" = "Favoritos";
"tab_settings" = "Ajustes";

// MARK: - Sound Categories
"category_rain" = "Lluvia";
"category_rain_desc" = "Diferentes sonidos de lluvia para calma y relajación";
"category_thunder" = "Truenos";
"category_thunder_desc" = "Sonidos de tormenta para ambiente dramático";
"category_wind" = "Viento";
"category_wind_desc" = "Desde brisa suave hasta viento fuerte";
"category_ocean" = "Océano";
"category_ocean_desc" = "Relajante sonido de olas";
"category_birds" = "Aves";
"category_birds_desc" = "Hermosos cantos de diferentes pájaros";
"category_forest" = "Bosque";
"category_forest_desc" = "Sonidos naturales del bosque";
"category_fire" = "Fuego";
"category_fire_desc" = "Cálido crepitar de fogata";
"category_city" = "Ciudad";
"category_city_desc" = "Sonidos ambientales urbanos";
"category_insects" = "Insectos";
"category_insects_desc" = "Sonidos de insectos en la naturaleza";

// MARK: - Sound Names
"rain_from_eaves" = "Lluvia bajo canaletas";
"light_rain" = "Lluvia ligera";
"heavy_rain" = "Lluvia intensa";
"distant_thunder" = "Trueno lejano";
"thunder_rumbles" = "Retumbo de truenos";
"high_altitude_thunder" = "Trueno en altura";
"undulating_thunder" = "Trueno ondulante";
"blows_leaves" = "Viento en hojas";
"strong_wind" = "Viento fuerte";
"winter_cold_wind" = "Viento frío invernal";
"desert_wind" = "Viento del desierto";
"breeze_rustles" = "Brisa susurrante";
"breeze_leaves" = "Brisa en hojas";
"water_wave" = "Ola de agua";
"sea_wave" = "Ola marina";
"waves_on_shore" = "Olas en la orilla";
"bubbling_underwater" = "Burbujas submarinas";
"small_stream" = "Arroyo pequeño";
"morning_birds" = "Aves mañaneras";
"forest_birds" = "Aves del bosque";
"nightingale" = "Ruiseñor";
"bird_chirping" = "Pájaros cantando";
"bird_outside" = "Pájaro afuera";
"distant_bird" = "Pájaro lejano";
"cuckoo" = "Cuco";
"cricket_chirping" = "Grillos";
"midsummer_insect_chirping" = "Insectos de verano";
"frog_sounds" = "Ranitas";
"forest_insects" = "Insectos del bosque";
"summer_evening_frog" = "Ranitas de verano";
"cicadas_chirping" = "Cigarras";
"bees_flying" = "Abejas volando";
"campfire" = "Fogata";
"fire_crackling" = "Crepiteo de fuego";
"flame_sound" = "Sonido de llama";
"city_ambience" = "Ambiente urbano";
"coffee_shop" = "Cafetería";
"flipping_books" = "Hojear libros";
"office_ambience" = "Oficina";
"typing_on_keyboard" = "Teclado";

"sound_rain_from_eaves" = "Lluvia bajo canaletas";
"sound_rain_on_leaves" = "Lluvia en hojas";
"sound_light_rain" = "Lluvia ligera";
"sound_heavy_rain" = "Lluvia intensa";
"sound_rain_with_thunder" = "Lluvia con truenos";
"sound_distant_thunder" = "Trueno lejano";
"sound_close_thunder" = "Trueno cercano";
"sound_thunderstorm" = "Tormenta eléctrica";
"sound_lightning_thunder" = "Rayo y trueno";
"sound_gentle_breeze" = "Brisa suave";
"sound_strong_wind" = "Viento fuerte";
"sound_wind_through_trees" = "Viento entre árboles";
"sound_mountain_wind" = "Viento de montaña";
"sound_waves_on_shore" = "Olas en la orilla";
"sound_deep_ocean_waves" = "Olas profundas";
"sound_beach_waves" = "Olas de playa";
"sound_seagulls_and_waves" = "Gaviotas y olas";
"sound_morning_birds" = "Aves mañaneras";
"sound_forest_birds" = "Aves del bosque";
"sound_nightingale" = "Ruiseñor";
"sound_bird_chirping" = "Pájaros cantando";
"sound_forest_ambience" = "Ambiente forestal";
"sound_rustling_leaves" = "Hojas susurrantes";
"sound_forest_stream" = "Arroyo forestal";
"sound_forest_insects" = "Insectos del bosque";
"sound_campfire" = "Fogata";
"sound_fireplace" = "Chimenea";
"sound_wood_burning" = "Madera quemándose";
"sound_city_ambience" = "Ambiente urbano";
"sound_cafe" = "Café";
"sound_library" = "Biblioteca";
"sound_office_ambience" = "Oficina";
"sound_cricket_chirping" = "Grillos";
"sound_midsummer_insect_chirping" = "Insectos de verano";
"sound_frog_sounds" = "Ranitas";
"sound_thunder_rumbles" = "Retumbo de truenos";
"sound_high_altitude_thunder" = "Trueno en altura";
"sound_undulating_thunder" = "Trueno ondulante";
"sound_blows_leaves" = "Viento en hojas";
"sound_winter_cold_wind" = "Viento frío invernal";
"sound_desert_wind" = "Viento del desierto";
"sound_breeze_rustles" = "Brisa susurrante";
"sound_breeze_leaves" = "Brisa en hojas";
"sound_water_wave" = "Ola de agua";
"sound_sea_wave" = "Ola marina";
"sound_bubbling_underwater" = "Burbujas submarinas";
"sound_small_stream" = "Arroyo pequeño";
"sound_bird_outside" = "Pájaro afuera";
"sound_distant_bird" = "Pájaro lejano";
"sound_cuckoo" = "Cuco";
"sound_summer_evening_frog" = "Ranitas de verano";
"sound_cicadas_chirping" = "Cigarras";
"sound_bees_flying" = "Abejas volando";
"sound_fire_crackling" = "Crepiteo de fuego";
"sound_flame_sound" = "Sonido de llama";
"sound_coffee_shop" = "Cafetería";
"sound_flipping_books" = "Hojear libros";
"sound_typing_on_keyboard" = "Teclado";

"sound_traffic" = "Tráfico";
"sound_cafe" = "Café";
"sound_subway" = "Metro";
"sound_construction" = "Construcción";
"sound_office" = "Oficina";
"sound_restaurant" = "Restaurante";
"sound_library" = "Biblioteca";
"sound_airport" = "Aeropuerto";

"sound_white_noise" = "Ruido blanco";
"sound_pink_noise" = "Ruido rosa";
"sound_brown_noise" = "Ruido marrón";
"sound_blue_noise" = "Ruido azul";
"sound_violet_noise" = "Ruido violeta";
"sound_grey_noise" = "Ruido gris";

"sound_space" = "Espacio";
"sound_underwater" = "Submarino";
"sound_cave" = "Cueva";
"sound_meditation" = "Meditación";
"sound_temple" = "Templo";
"sound_monastery" = "Monasterio";

// MARK: - Sound Descriptions
"rain_from_eaves_desc" = "Sonido de lluvia cayendo de canaletas";
"light_rain_desc" = "Suave sonido de lluvia ligera";
"heavy_rain_desc" = "Sonido intenso de lluvia fuerte";
"distant_thunder_desc" = "Sonido de truenos a distancia";
"thunder_rumbles_desc" = "Sonido profundo de truenos retumbantes";
"high_altitude_thunder_desc" = "Sonido de truenos en altura";
"undulating_thunder_desc" = "Sonido de truenos ondulantes";
"blows_leaves_desc" = "Sonido de viento moviendo hojas";
"strong_wind_desc" = "Sonido de viento fuerte";
"winter_cold_wind_desc" = "Sonido de viento frío invernal";
"desert_wind_desc" = "Sonido de viento seco del desierto";
"breeze_rustles_desc" = "Suave sonido de brisa susurrante";
"breeze_leaves_desc" = "Sonido de brisa acariciando hojas";
"water_wave_desc" = "Suave sonido de olas de agua";
"sea_wave_desc" = "Sonido de olas del mar";
"waves_on_shore_desc" = "Sonido de olas rompiendo en la orilla";
"bubbling_underwater_desc" = "Sonido de burbujas bajo el agua";
"small_stream_desc" = "Sonido de pequeño arroyo fluyendo";
"morning_birds_desc" = "Coro matutino de pájaros";
"forest_birds_desc" = "Cantos de pájaros en el bosque";
"nightingale_desc" = "Melodioso canto del ruiseñor";
"bird_chirping_desc" = "Diversos cantos de pájaros";
"bird_outside_desc" = "Canto de pájaro afuera";
"distant_bird_desc" = "Canto de pájaro a distancia";
"cuckoo_desc" = "Canto del cuco";
"cricket_chirping_desc" = "Sonido de grillos en la noche";
"midsummer_insect_chirping_desc" = "Diversos sonidos de insectos veraniegos";
"frog_sounds_desc" = "Pacífico croar de ranas";
"forest_insects_desc" = "Sonidos de insectos en el bosque";
"summer_evening_frog_desc" = "Coro de ranas en tardes de verano";
"cicadas_chirping_desc" = "Canto de cigarras en verano";
"bees_flying_desc" = "Zumbido de abejas volando";
"campfire_desc" = "Sonido crepitante de fogata";
"fire_crackling_desc" = "Sonido crepitante de fuego";
"flame_sound_desc" = "Sonido constante de llama ardiendo";
"city_ambience_desc" = "Sonidos ambientales urbanos";
"coffee_shop_desc" = "Ambiente de cafetería";
"flipping_books_desc" = "Sonido de hojas pasándose";
"office_ambience_desc" = "Sonidos de fondo de oficina";
"typing_on_keyboard_desc" = "Sonido de teclado";

"sound_rain_on_roof_desc" = "Sonido de lluvia en techo";
"sound_rain_on_leaves_desc" = "Sonido de lluvia en hojas";
"sound_light_rain_desc" = "Suave sonido de lluvia ligera";
"sound_heavy_rain_desc" = "Sonido intenso de lluvia fuerte";
"sound_rain_with_thunder_desc" = "Lluvia con truenos distantes";
"sound_distant_thunder_desc" = "Sonido de truenos a distancia";
"sound_close_thunder_desc" = "Sonido de truenos cercanos";
"sound_thunderstorm_desc" = "Combinación perfecta de truenos y lluvia";
"sound_lightning_thunder_desc" = "Truenos después de relámpagos";
"sound_gentle_breeze_desc" = "Sonido de brisa suave";
"sound_strong_wind_desc" = "Sonido de viento fuerte";
"sound_wind_through_trees_desc" = "Sonido de viento entre árboles";
"sound_mountain_wind_desc" = "Sonido de viento de montaña";
"sound_waves_on_shore_desc" = "Sonido de olas rompiendo en la orilla";
"sound_deep_ocean_waves_desc" = "Sonido de olas profundas";
"sound_beach_waves_desc" = "Sonido de olas en playa";
"sound_seagulls_and_waves_desc" = "Gaviotas y olas";
"sound_morning_birds_desc" = "Coro matutino de pájaros";
"sound_forest_birds_desc" = "Cantos de pájaros en el bosque";
"sound_nightingale_desc" = "Melodioso canto del ruiseñor";
"sound_bird_chirping_desc" = "Diversos cantos de pájaros";
"sound_forest_ambience_desc" = "Ambiente sonoro forestal";
"sound_rustling_leaves_desc" = "Sonido de hojas susurrantes";
"sound_forest_stream_desc" = "Sonido de arroyo en bosque";
"sound_forest_insects_desc" = "Sonidos de insectos en el bosque";
"sound_campfire_desc" = "Sonido crepitante de fogata";
"sound_fireplace_desc" = "Sonido de chimenea";
"sound_wood_burning_desc" = "Sonido de madera quemándose";
"sound_city_ambience_desc" = "Sonidos ambientales urbanos";
"sound_cafe_desc" = "Ambiente de café";
"sound_library_desc" = "Ambiente silencioso de biblioteca";
"sound_office_ambience_desc" = "Sonidos de fondo de oficina";
"sound_midsummer_insect_chirping_desc" = "Diversos sonidos de insectos veraniegos";
"sound_frog_sounds_desc" = "Pacífico croar de ranas";
"sound_thunder_rumbles_desc" = "Sonido profundo de truenos retumbantes";
"sound_high_altitude_thunder_desc" = "Sonido de truenos en altura";
"sound_undulating_thunder_desc" = "Sonido de truenos ondulantes";
"sound_blows_leaves_desc" = "Viento moviendo hojas afuera";
"sound_winter_cold_wind_desc" = "Sonido de viento frío invernal";
"sound_desert_wind_desc" = "Sonido de viento seco del desierto";
"sound_breeze_rustles_desc" = "Suave sonido de brisa susurrante";
"sound_breeze_leaves_desc" = "Sonido de brisa acariciando hojas";
"sound_water_wave_desc" = "Suave sonido de olas de agua";
"sound_sea_wave_desc" = "Sonido de olas del mar";
"sound_bubbling_underwater_desc" = "Sonido de burbujas bajo el agua";
"sound_small_stream_desc" = "Sonido de pequeño arroyo fluyendo";
"sound_bird_outside_desc" = "Canto de pájaro afuera";
"sound_distant_bird_desc" = "Canto de pájaro a distancia";
"sound_cuckoo_desc" = "Canto del cuco";
"sound_summer_evening_frog_desc" = "Coro de ranas en tardes de verano";
"sound_cicadas_chirping_desc" = "Canto de cigarras en verano";
"sound_bees_flying_desc" = "Zumbido de abejas volando";
"sound_fire_crackling_desc" = "Sonido crepitante de fuego";
"sound_flame_sound_desc" = "Sonido constante de llama ardiendo";
"sound_coffee_shop_desc" = "Ambiente de cafetería";
"sound_flipping_books_desc" = "Sonido de hojas pasándose";
"sound_typing_on_keyboard_desc" = "Sonido de teclado";
"sound_rain_from_eaves_desc" = "Lluvia cayendo de canaletas al suelo";
"sound_cricket_chirping_desc" = "Suave sonido de grillos";

"sound_traffic_desc" = "Sonido de fondo de tráfico urbano";
"sound_cafe_desc" = "Ambiente de café";
"sound_subway_desc" = "Sonido de metro";
"sound_construction_desc" = "Ruido de construcción";
"sound_office_desc" = "Ambiente de oficina";
"sound_restaurant_desc" = "Fondo de restaurante";
"sound_library_desc" = "Sonido silencioso de biblioteca";
"sound_airport_desc" = "Ambiente de aeropuerto";

"sound_white_noise_desc" = "Ruido blanco clásico";
"sound_pink_noise_desc" = "Ruido rosa suave";
"sound_brown_noise_desc" = "Ruido marrón profundo";
"sound_blue_noise_desc" = "Ruido azul claro";
"sound_violet_noise_desc" = "Ruido violeta de alta frecuencia";
"sound_grey_noise_desc" = "Ruido gris equilibrado";

"sound_space_desc" = "Sonidos misteriosos del espacio";
"sound_underwater_desc" = "Calma submarina";
"sound_cave_desc" = "Ecos de cueva";
"sound_meditation_desc" = "Fondo para meditación";
"sound_temple_desc" = "Campanas de templo";
"sound_monastery_desc" = "Tranquilidad monástica";

// MARK: - UI Elements
"sound_count" = "%d sonidos";
"playing_count" = "%d/%d reproduciendo";
"volume" = "Volumen";

// MARK: - Buttons
"play" = "Reproducir";
"pause" = "Pausa";
"stop" = "Detener";
"stop_all" = "Detener todo";
"save" = "Guardar";
"cancel" = "Cancelar";
"delete" = "Eliminar";
"edit" = "Editar";
"done" = "Hecho";
"close" = "Cerrar";
"back" = "Atrás";

// MARK: - Mixer
"mixer_title" = "Mezclador";
"active_sounds_count" = "%d sonidos activos";
"no_active_sounds" = "Sin sonidos activos";
"no_active_sounds_desc" = "Vuelve a la página principal para seleccionar sonidos y comenzar a mezclar";
"save_mix" = "Guardar mezcla";
"save_as_new_mix" = "Guardar como nueva mezcla";
"update_current_mix" = "Guardar";
"update_and_add_sounds" = "Actualizar y añadir sonidos";
"update_mix" = "Actualizar mezcla";
"update_mix_prompt" = "¿Actualizar esta mezcla con la configuración actual?";
"saved_mixes" = "Mezclas guardadas";
"mix_name" = "Nombre de mezcla";
"mix_description" = "Descripción";
"mix_name_placeholder" = "Nombre de mezcla";
"mix_description_placeholder" = "Descripción (opcional)";
"mix_name_prompt" = "Asigna un nombre a tu mezcla actual";
"unnamed_mix" = "Mezcla sin nombre";

// MARK: - Favorites
"favorites_title" = "Mis Favoritos";
"favorites_subtitle" = "Mezclas de sonido guardadas";
"favorites_count" = "favoritos";
"no_favorites" = "Sin favoritos";
"no_favorites_desc" = "Guarda tus combinaciones de sonido favoritas en el mezclador";
"delete_favorite" = "Eliminar favorito";
"delete_favorite_message" = "¿Eliminar esta mezcla favorita?";
"mix_sounds_count" = "%d sonidos";
"mix_combination" = "Combinación de mezcla";

// MARK: - Settings
"settings_title" = "Ajustes";
"settings_subtitle" = "Personaliza tu aplicación";
"appearance_section" = "Apariencia";
"language_section" = "Idioma";
"playback_section" = "Reproducción";
"about_section" = "Acerca de";

// Apariencia
"theme_color" = "Color del tema";
"dark_mode" = "Modo oscuro";
"follow_system" = "Seguir sistema";

// Idioma
"app_language" = "Idioma de la app";
"system_language_settings" = "Ir a ajustes del sistema";

// Reproducción
"auto_play" = "Reproducción automática";
"auto_play_desc" = "Reanudar la última mezcla al abrir la app";
"background_play" = "Reproducción en segundo plano";
"background_play_desc" = "Continuar reproduciendo al cambiar de app";
"lock_screen_control" = "Control en pantalla bloqueada";
"lock_screen_control_desc" = "Controlar la reproducción directamente desde pantalla bloqueada y centro de control";

// Lock Screen Control VIP
"lock_screen_control_title" = "Control en pantalla bloqueada";
"lock_screen_control_vip_desc" = "Actualiza a VIP para controlar desde pantalla bloqueada";
"lock_screen_control_feature" = "Control en pantalla bloqueada";
"lock_screen_control_feature_desc" = "Controlar reproducción desde pantalla bloqueada";
"lock_screen_control_benefit1" = "Control sin desbloquear dispositivo";
"lock_screen_control_benefit2" = "Acceso rápido desde centro de control";
"lock_screen_control_benefit3" = "Integración con controles multimedia de iOS";

// Mensajes reproducción automática
"auto_play_enabled" = "Reproducción automática activada";
"auto_play_disabled" = "Reproducción automática desactivada";
"no_last_mix_found" = "No se encontró última mezcla";
"last_mix_loaded" = "Última mezcla cargada";
"auto_play_mix_name" = "Mezcla auto-reproducir";
"auto_play_mix_desc" = "Mezcla guardada para reproducción automática";

// Mensajes reproducción en segundo plano
"background_play_enabled" = "Reproducción en segundo plano activada";
"background_play_disabled" = "Reproducción en segundo plano desactivada";

// Acerca de
"version" = "Versión";
"feedback" = "Comentarios";
"feedback_desc" = "Enviar comentarios y sugerencias";
"rate_app" = "Calificar";
"rate_app_desc" = "Calificar en App Store";

// MARK: - Timer
"timer" = "Temporizador";
"timer_remaining" = "Quedan %@";
"set_timer" = "Establecer temporizador";
"timer_minutes" = "minutos";
"timer_hours" = "horas";

// MARK: - Alerts & Messages
"error" = "Error";
"success" = "Éxito";
"loading" = "Cargando...";
"saved_successfully" = "Guardado correctamente";
"deleted_successfully" = "Eliminado correctamente";

// MARK: - Accessibility
"play_button" = "Botón reproducir";
"pause_button" = "Botón pausa";
"volume_slider" = "Control de volumen";
"close_button" = "Botón cerrar";

// MARK: - VIP Subscription
// Títulos características VIP
"vip_sound_title" = "Sonidos VIP exclusivos";
"mix_limit_title" = "Mezclas ilimitadas";
"save_limit_title" = "Favoritos ilimitados";
"vip_theme_title" = "Temas VIP exclusivos";
"long_timer_title" = "Temporizador largo";

// Descripciones características VIP
"vip_sound_desc" = "¡Este sonido requiere suscripción VIP! Actualiza a VIP para desbloquear todos los sonidos exclusivos de alta calidad";
"mix_limit_desc" = "Usuarios gratuitos solo pueden reproducir %d sonidos simultáneamente. ¡Actualiza a VIP para mezclas ilimitadas!";
"save_limit_desc" = "Usuarios gratuitos solo pueden guardar %d favoritos. ¡Actualiza a VIP para favoritos ilimitados!";
"vip_theme_desc" = "¡Este tema requiere suscripción VIP! Actualiza a VIP para desbloquear todos los hermosos temas";
"long_timer_desc" = "Usuarios gratuitos solo pueden establecer temporizadores de hasta 30 minutos. ¡Actualiza a VIP para temporizadores más largos!";

// Beneficios VIP
"upgrade_will_get" = "Al actualizar obtendrás:";
"all_vip_benefits" = "Todos los beneficios VIP";

// Beneficios sonidos VIP
"vip_sound_benefit_1" = "Desbloquea todos los sonidos VIP exclusivos";
"vip_sound_benefit_2" = "Experiencia de audio de alta calidad";
"vip_sound_benefit_3" = "Contenido sonoro exclusivo";

// Beneficios mezclas ilimitadas
"mix_limit_benefit_1" = "Añade sonidos ilimitados a tus mezclas";
"mix_limit_benefit_2" = "Crea combinaciones de sonido complejas";
"mix_limit_benefit_3" = "Experiencia de mezcla profesional";

// Beneficios favoritos ilimitados
"save_limit_benefit_1" = "Guarda ilimitadas mezclas favoritas";
"save_limit_benefit_2" = "Crea tu biblioteca de sonido personal";
"save_limit_benefit_3" = "Accede a tus mezclas favoritas en cualquier momento";

// Beneficios temas VIP
"vip_theme_benefit_1" = "Desbloquea todos los hermosos temas";
"vip_theme_benefit_2" = "Experiencia de interfaz personalizada";
"vip_theme_benefit_3" = "Diseño visual exclusivo";

// Beneficios temporizador largo
"long_timer_benefit_1" = "Establece temporizadores más largos de 30 minutos";
"long_timer_benefit_2" = "Extiende tu tiempo de relajación";
"long_timer_benefit_3" = "Perfecto para dormir y meditar";

// Lista características VIP
"vip_sound_feature" = "Sonidos VIP exclusivos";
"vip_sound_feature_desc" = "Desbloquea sonidos exclusivos de alta calidad";
"mix_limit_feature" = "Mezclas ilimitadas";
"mix_limit_feature_desc" = "Añade sonidos ilimitados";
"save_limit_feature" = "Favoritos ilimitados";
"save_limit_feature_desc" = "Guarda ilimitados favoritos";
"vip_theme_feature" = "Temas VIP exclusivos";
"vip_theme_feature_desc" = "Usa todos los hermosos temas";
"long_timer_feature" = "Temporizador largo";
"long_timer_feature_desc" = "Temporizadores más largos de 30 minutos";

// Botones de acción
"upgrade_to_vip" = "Actualizar a VIP";
"later_button" = "Más tarde";
"tap_to_upgrade" = "Toca para actualizar";

// MARK: - Theme Names
"theme_ocean_breeze" = "Brisa marina";
"theme_sunset_glow" = "Resplandor del atardecer";
"theme_forest_mist" = "Niebla forestal";
"theme_night_sky" = "Cielo nocturno";
"theme_lavender_dream" = "Sueño de lavanda";
"theme_fire_ember" = "Brasas ardientes";

// MARK: - Theme Selector
"select_theme" = "Seleccionar para usar";
"current_theme" = "Actual: %@";
"theme_in_use" = "En uso";
"previous_theme" = "Anterior";
"next_theme" = "Siguiente";

// Confirmación cambio tema
"confirm_theme_change" = "Confirmar cambio de tema";
"confirm_theme_change_message" = "¿Cambiar al tema \"%@\"?";
"confirm" = "Confirmar";

// Confirmación eliminar favorito
"delete_favorite_confirm" = "Eliminar mezcla favorita";
"delete_favorite_message" = "Eliminar el último sonido también eliminará la mezcla favorita \"%@\". ¿Continuar?";
"delete_favorite" = "Eliminar favorito";

// MARK: - Timer
"timer" = "Temporizador";
"time_remaining" = "Tiempo restante";
"set_timer" = "Establecer temporizador";
"timer_description" = "Selecciona la duración de reproducción, se detendrá automáticamente";
"quick_select" = "Selección rápida";
"custom_time" = "Tiempo personalizado";
"hours" = "horas";
"minutes" = "minutos";
"seconds" = "segundos";
"start_timer" = "Iniciar temporizador";
"cancel_timer" = "Cancelar temporizador";
"elapsed_time" = "Reproduciendo durante %@";

// Duración temporizadores
"timer_15min" = "15 minutos";
"timer_30min" = "30 minutos";
"timer_45min" = "45 minutos";
"timer_1hour" = "1 hora";
"timer_1_5hour" = "1.5 horas";
"timer_2hour" = "2 horas";

// Información reproducción actual
"playing_count" = "%d/%d sonidos reproduciendo";
"active_sounds_count" = "%d sonidos activos";
"app_subtitle" = "Mezclador de sonidos ambientales";

// MARK: - Subscription Plans
"monthly_subscription" = "Suscripción mensual";
"yearly_subscription" = "Suscripción anual";
"lifetime_purchase" = "Compra de por vida";
"popular_badge" = "Popular";
"best_value_badge" = "Mejor valor";
"one_time_purchase" = "Compra única";
"cancel_anytime" = "Cancela en cualquier momento";
"save_over_30_percent" = "Ahorra más de 30% vs suscripción mensual";
"lifetime_description" = "Compra una vez, disfruta todas las funciones VIP para siempre";
"choose_subscription_plan" = "Elige un plan de suscripción";
"loading_subscription_options" = "Cargando opciones de suscripción...";
"restore_purchases" = "Restaurar compras";
"terms_of_service" = "Términos de servicio";
"privacy_policy" = "Política de privacidad";
"subscription_success" = "¡Suscripción exitosa! ¡Gracias por tu apoyo!";
"purchase_failed" = "Compra fallida: %@";
"restore_success" = "¡Compras restauradas!";
"no_purchases_to_restore" = "No hay compras para restaurar";
"processing" = "Procesando...";

// MARK: - Subscription Interface
"upgrade_to_vip_title" = "Actualizar a VIP";
"upgrade_subtitle" = "Desbloquea todas las funciones premium y la experiencia completa de DIY Noise";
"vip_membership_benefits" = "Beneficios de membresía VIP";
"preview_mode_notice" = "Modo de vista previa - Configura tus productos en App Store Connect";

// Período suscripción
"daily" = "Diario";
"weekly" = "Semanal";
"monthly" = "Mensual";
"yearly" = "Anual";

// Características VIP
"vip_exclusive_sounds" = "Sonidos VIP exclusivos";
"vip_exclusive_sounds_desc" = "Desbloquea todos los sonidos exclusivos de alta calidad";
"unlimited_mixing" = "Mezclas ilimitadas";
"unlimited_mixing_desc" = "Añade sonidos ilimitados a tus mezclas";
"unlimited_favorites" = "Favoritos ilimitados";
"unlimited_favorites_desc" = "Guarda ilimitadas mezclas favoritas";
"vip_exclusive_themes" = "Temas VIP exclusivos";
"vip_exclusive_themes_desc" = "Usa todos los hermosos temas de interfaz";
"lock_screen_control_feature" = "Control en pantalla bloqueada";
"lock_screen_control_feature_desc" = "Controla la reproducción desde pantalla bloqueada";
"extended_timer" = "Temporizador extendido";
"extended_timer_desc" = "Temporizadores más largos de 30 minutos";

// Alerta
"alert_title" = "Aviso";
"ok_button" = "OK";

/* 
  Localizable.strings (Chinese Simplified)
  DIYNoise
*/

// MARK: - App Name & Main
"app_name" = "DIY Noise";
"app_subtitle" = "创造你的专属白噪音";

// MARK: - Tab Bar
"tab_sounds" = "声音";
"tab_favorites" = "收藏";
"tab_settings" = "设置";

// MARK: - Sound Categories
"category_rain" = "雨声";
"category_rain_desc" = "各种雨声，带来宁静的感觉";
"category_thunder" = "雷声";
"category_thunder_desc" = "雷鸣声，营造戏剧性氛围";
"category_wind" = "风声";
"category_wind_desc" = "微风到狂风的各种风声";
"category_ocean" = "海浪声";
"category_ocean_desc" = "海浪拍打的舒缓声音";
"category_birds" = "鸟叫声";
"category_birds_desc" = "各种鸟类的美妙歌声";
"category_forest" = "森林声";
"category_forest_desc" = "森林中的自然声音";
"category_fire" = "火焰声";
"category_fire_desc" = "篝火燃烧的温暖声音";
"category_city" = "城市声";
"category_city_desc" = "城市环境的背景声音";
"category_insects" = "虫鸣声";
"category_insects_desc" = "大自然中昆虫的声音";

// MARK: - Sound Names
"rain_from_eaves" = "屋檐下的雨";
"light_rain" = "小雨声";
"heavy_rain" = "暴雨声";
"distant_thunder" = "远处雷声";
"thunder_rumbles" = "雷声轰隆";
"high_altitude_thunder" = "高空雷声";
"undulating_thunder" = "起伏的雷声";
"blows_leaves" = "风吹树叶";
"strong_wind" = "强风";
"winter_cold_wind" = "冬日寒风";
"desert_wind" = "沙漠风声";
"breeze_rustles" = "微风沙沙";
"breeze_leaves" = "微风拂叶";
"water_wave" = "水波声";
"sea_wave" = "海浪声";
"waves_on_shore" = "海浪拍岸";
"bubbling_underwater" = "水下气泡";
"small_stream" = "小溪流水";
"morning_birds" = "晨鸟合唱";
"forest_birds" = "森林鸟鸣";
"nightingale" = "夜莺歌声";
"bird_chirping" = "鸟儿啁啾";
"bird_outside" = "窗外鸟鸣";
"distant_bird" = "远处鸟叫";
"cuckoo" = "布谷鸟";
"cricket_chirping" = "蟋蟀叫声";
"midsummer_insect_chirping" = "仲夏昆虫鸣叫";
"frog_sounds" = "蛙声";
"forest_insects" = "虫鸣声";
"summer_evening_frog" = "夏夜蛙鸣";
"cicadas_chirping" = "蝉鸣声";
"bees_flying" = "蜜蜂飞舞";
"campfire" = "篝火燃烧";
"fire_crackling" = "火焰噼啪";
"flame_sound" = "火焰燃烧";
"city_ambience" = "城市环境音";
"coffee_shop" = "咖啡店";
"flipping_books" = "翻书声";
"office_ambience" = "办公室";
"typing_on_keyboard" = "键盘敲击";

"sound_rain_from_eaves" = "屋檐下的雨";
"sound_rain_on_leaves" = "雨声打在树叶";
"sound_light_rain" = "小雨声";
"sound_heavy_rain" = "暴雨声";
"sound_rain_with_thunder" = "雨声配雷声";
"sound_distant_thunder" = "远处雷声";
"sound_close_thunder" = "近处雷声";
"sound_thunderstorm" = "雷雨交加";
"sound_lightning_thunder" = "闪电雷鸣";
"sound_gentle_breeze" = "微风";
"sound_strong_wind" = "强风";
"sound_wind_through_trees" = "风吹树叶";
"sound_mountain_wind" = "山风";
"sound_waves_on_shore" = "海浪拍岸";
"sound_deep_ocean_waves" = "深海波浪";
"sound_beach_waves" = "海滩声";
"sound_seagulls_and_waves" = "海鸥与海浪";
"sound_morning_birds" = "晨鸟合唱";
"sound_forest_birds" = "森林鸟鸣";
"sound_nightingale" = "夜莺歌声";
"sound_bird_chirping" = "鸟儿啁啾";
"sound_forest_ambience" = "森林环境音";
"sound_rustling_leaves" = "树叶沙沙声";
"sound_forest_stream" = "小溪流水";
"sound_forest_insects" = "虫鸣声";
"sound_campfire" = "篝火燃烧";
"sound_fireplace" = "壁炉火焰";
"sound_wood_burning" = "木柴燃烧";
"sound_city_ambience" = "城市环境音";
"sound_cafe" = "咖啡厅";
"sound_library" = "图书馆";
"sound_office_ambience" = "办公室";
"sound_cricket_chirping" = "蟋蟀叫声";
"sound_midsummer_insect_chirping" = "仲夏昆虫鸣叫";
"sound_frog_sounds" = "蛙声";
"sound_thunder_rumbles" = "雷声轰隆";
"sound_high_altitude_thunder" = "高空雷声";
"sound_undulating_thunder" = "起伏的雷声";
"sound_blows_leaves" = "风吹树叶";
"sound_winter_cold_wind" = "冬日寒风";
"sound_desert_wind" = "沙漠风声";
"sound_breeze_rustles" = "微风沙沙";
"sound_breeze_leaves" = "微风拂叶";
"sound_water_wave" = "水波声";
"sound_sea_wave" = "海浪声";
"sound_bubbling_underwater" = "水下气泡";
"sound_small_stream" = "小溪流水";
"sound_bird_outside" = "窗外鸟鸣";
"sound_distant_bird" = "远处鸟叫";
"sound_cuckoo" = "布谷鸟";
"sound_summer_evening_frog" = "夏夜蛙鸣";
"sound_cicadas_chirping" = "蝉鸣声";
"sound_bees_flying" = "蜜蜂飞舞";
"sound_fire_crackling" = "火焰噼啪";
"sound_flame_sound" = "火焰燃烧";
"sound_coffee_shop" = "咖啡店";
"sound_flipping_books" = "翻书声";
"sound_typing_on_keyboard" = "键盘敲击";

"sound_traffic" = "交通声";
"sound_cafe" = "咖啡厅";
"sound_subway" = "地铁声";
"sound_construction" = "施工声";
"sound_office" = "办公室";
"sound_restaurant" = "餐厅";
"sound_library" = "图书馆";
"sound_airport" = "机场";

"sound_white_noise" = "白噪音";
"sound_pink_noise" = "粉噪音";
"sound_brown_noise" = "棕噪音";
"sound_blue_noise" = "蓝噪音";
"sound_violet_noise" = "紫噪音";
"sound_grey_noise" = "灰噪音";

"sound_space" = "太空";
"sound_underwater" = "水下";
"sound_cave" = "洞穴";
"sound_meditation" = "冥想";
"sound_temple" = "寺庙";
"sound_monastery" = "修道院";

// MARK: - Sound Descriptions
"rain_from_eaves_desc" = "雨水从屋檐滴落的声音";
"light_rain_desc" = "轻柔的小雨声";
"heavy_rain_desc" = "激烈的暴雨声";
"distant_thunder_desc" = "远方传来的雷鸣声";
"thunder_rumbles_desc" = "深沉的雷声轰隆";
"high_altitude_thunder_desc" = "高空传来的雷声";
"undulating_thunder_desc" = "起伏不定的雷声";
"blows_leaves_desc" = "风吹动树叶的声音";
"strong_wind_desc" = "强劲的风声";
"winter_cold_wind_desc" = "冬日里呼啸的寒风";
"desert_wind_desc" = "干燥的沙漠风声";
"breeze_rustles_desc" = "轻柔的微风沙沙声";
"breeze_leaves_desc" = "微风轻抚树叶的声音";
"water_wave_desc" = "轻柔的水波拍打声";
"sea_wave_desc" = "海浪翻滚的声音";
"waves_on_shore_desc" = "海浪拍打海岸的声音";
"bubbling_underwater_desc" = "水下的气泡声";
"small_stream_desc" = "小溪缓缓流淌的声音";
"morning_birds_desc" = "清晨的鸟儿合唱";
"forest_birds_desc" = "森林中的鸟叫声";
"nightingale_desc" = "夜莺的美妙歌声";
"bird_chirping_desc" = "各种鸟儿的啁啾声";
"bird_outside_desc" = "窗外鸟儿的歌声";
"distant_bird_desc" = "远处传来的鸟叫声";
"cuckoo_desc" = "布谷鸟的叫声";
"cricket_chirping_desc" = "夜晚蟋蟀的叫声";
"midsummer_insect_chirping_desc" = "仲夏的各种混合虫鸣";
"frog_sounds_desc" = "宁静的蛙叫声";
"forest_insects_desc" = "森林中的虫鸣声";
"summer_evening_frog_desc" = "夏夜的蛙鸣合唱";
"cicadas_chirping_desc" = "夏日蝉鸣声";
"bees_flying_desc" = "蜜蜂飞舞的嗡嗡声";
"campfire_desc" = "篝火燃烧噼啪的声音";
"fire_crackling_desc" = "火焰噼啪燃烧的声音";
"flame_sound_desc" = "稳定的火焰燃烧声";
"city_ambience_desc" = "城市的背景环境声";
"coffee_shop_desc" = "咖啡店的环境声";
"flipping_books_desc" = "翻书页的声音";
"office_ambience_desc" = "办公室的背景声音";
"typing_on_keyboard_desc" = "键盘敲击的声音";

"sound_rain_on_roof_desc" = "雨滴敲击屋顶的声音";
"sound_rain_on_leaves_desc" = "雨水滴落在树叶上的声音";
"sound_light_rain_desc" = "轻柔的小雨声";
"sound_heavy_rain_desc" = "激烈的暴雨声";
"sound_rain_with_thunder_desc" = "雨声伴随远处雷声";
"sound_distant_thunder_desc" = "远方传来的雷鸣声";
"sound_close_thunder_desc" = "近距离的雷声";
"sound_thunderstorm_desc" = "雷声与雨声的完美结合";
"sound_lightning_thunder_desc" = "闪电后的雷鸣声";
"sound_gentle_breeze_desc" = "轻柔的微风声";
"sound_strong_wind_desc" = "强劲的风声";
"sound_wind_through_trees_desc" = "风吹过树叶的声音";
"sound_mountain_wind_desc" = "山间的风声";
"sound_waves_on_shore_desc" = "海浪拍打海岸的声音";
"sound_deep_ocean_waves_desc" = "深海中的波浪声";
"sound_beach_waves_desc" = "海滩上的海浪声";
"sound_seagulls_and_waves_desc" = "海鸥叫声配海浪声";
"sound_morning_birds_desc" = "清晨的鸟儿合唱";
"sound_forest_birds_desc" = "森林中的鸟叫声";
"sound_nightingale_desc" = "夜莺的美妙歌声";
"sound_bird_chirping_desc" = "各种鸟儿的啁啾声";
"sound_forest_ambience_desc" = "森林的整体环境声";
"sound_rustling_leaves_desc" = "树叶摩擦的声音";
"sound_forest_stream_desc" = "森林中小溪的流水声";
"sound_forest_insects_desc" = "森林中的虫鸣声";
"sound_campfire_desc" = "篝火燃烧的声音";
"sound_fireplace_desc" = "壁炉中火焰的声音";
"sound_wood_burning_desc" = "木柴燃烧的声音";
"sound_city_ambience_desc" = "城市的背景环境声";
"sound_cafe_desc" = "咖啡厅的环境声";
"sound_library_desc" = "图书馆的安静环境声";
"sound_office_ambience_desc" = "办公室的背景声音";
"sound_midsummer_insect_chirping_desc" = "仲夏的各种混合虫鸣";
"sound_frog_sounds_desc" = "宁静的蛙叫声";
"sound_thunder_rumbles_desc" = "深沉的雷声轰隆";
"sound_high_altitude_thunder_desc" = "高空传来的雷声";
"sound_undulating_thunder_desc" = "起伏不定的雷声";
"sound_blows_leaves_desc" = "风吹动窗外的树叶";
"sound_winter_cold_wind_desc" = "冬日里呼啸的寒风";
"sound_desert_wind_desc" = "干燥的沙漠风声";
"sound_breeze_rustles_desc" = "轻柔的微风沙沙声";
"sound_breeze_leaves_desc" = "微风轻抚树叶的声音";
"sound_water_wave_desc" = "轻柔的水波拍打声";
"sound_sea_wave_desc" = "海浪翻滚的声音";
"sound_bubbling_underwater_desc" = "水下的气泡声";
"sound_small_stream_desc" = "小溪缓缓流淌的声音";
"sound_bird_outside_desc" = "窗外鸟儿的歌声";
"sound_distant_bird_desc" = "远处传来的鸟叫声";
"sound_cuckoo_desc" = "布谷鸟的叫声";
"sound_summer_evening_frog_desc" = "夏夜的蛙鸣合唱";
"sound_cicadas_chirping_desc" = "夏日蝉鸣声";
"sound_bees_flying_desc" = "蜜蜂飞舞的嗡嗡声";
"sound_fire_crackling_desc" = "火焰噼啪燃烧的声音";
"sound_flame_sound_desc" = "稳定的火焰燃烧声";
"sound_coffee_shop_desc" = "咖啡店的环境声";
"sound_flipping_books_desc" = "翻书页的声音";
"sound_typing_on_keyboard_desc" = "键盘敲击的声音";
"sound_rain_from_eaves_desc" = "屋檐下的雨打落在地上";
"sound_cricket_chirping_desc" = "安静的蟋蟀的叫声";

"sound_traffic_desc" = "城市交通的背景声";
"sound_cafe_desc" = "咖啡厅的环境声";
"sound_subway_desc" = "地铁运行的声音";
"sound_construction_desc" = "建筑工地的声音";
"sound_office_desc" = "办公室的环境声";
"sound_restaurant_desc" = "餐厅的背景声";
"sound_library_desc" = "图书馆的安静声";
"sound_airport_desc" = "机场的环境声";

"sound_white_noise_desc" = "经典的白噪音";
"sound_pink_noise_desc" = "温和的粉噪音";
"sound_brown_noise_desc" = "深沉的棕噪音";
"sound_blue_noise_desc" = "清脆的蓝噪音";
"sound_violet_noise_desc" = "高频紫噪音";
"sound_grey_noise_desc" = "平衡的灰噪音";

"sound_space_desc" = "太空的神秘声音";
"sound_underwater_desc" = "水下的宁静声";
"sound_cave_desc" = "洞穴的回声";
"sound_meditation_desc" = "冥想的背景声";
"sound_temple_desc" = "寺庙的钟声";
"sound_monastery_desc" = "修道院的宁静";

// MARK: - UI Elements
"sound_count" = "%d 个声音";
"playing_count" = "%d/%d 播放中";
"volume" = "音量";

// MARK: - Buttons
"play" = "播放";
"pause" = "暂停";
"stop" = "停止";
"stop_all" = "停止全部";
"save" = "保存";
"cancel" = "取消";
"delete" = "删除";
"edit" = "编辑";
"done" = "完成";
"close" = "关闭";
"back" = "返回";

// MARK: - Mixer
"mixer_title" = "混音器";
"active_sounds_count" = "%d 个活跃声音";
"no_active_sounds" = "没有活跃的声音";
"no_active_sounds_desc" = "返回主页面选择声音开始混音";
"save_mix" = "保存混音";
"save_as_new_mix" = "保存为新混音";
"update_current_mix" = "保存";
"update_and_add_sounds" = "更新并添加声音";
"update_mix" = "更新混音";
"update_mix_prompt" = "确定要用当前设置更新这个混音吗？";
"saved_mixes" = "已保存的混音";
"mix_name" = "混音名称";
"mix_description" = "描述";
"mix_name_placeholder" = "混音名称";
"mix_description_placeholder" = "描述（可选）";
"mix_name_prompt" = "为当前混音设置一个名称";
"unnamed_mix" = "未命名混音";

// MARK: - Favorites
"favorites_title" = "我的收藏";
"favorites_subtitle" = "保存的混音组合";
"favorites_count" = "个收藏";
"no_favorites" = "暂无收藏";
"no_favorites_desc" = "在混音器中保存你喜欢的声音组合";
"delete_favorite" = "删除收藏";
"delete_favorite_message" = "确定要删除这个收藏的混音吗？";
"mix_sounds_count" = "%d 个声音";
"mix_combination" = "混音组合";

// MARK: - Settings
"settings_title" = "设置";
"settings_subtitle" = "个性化你的应用";
"appearance_section" = "系统";
"language_section" = "语言";
"playback_section" = "播放";
"about_section" = "关于";

// 外观设置
"theme_color" = "主题色彩";
"dark_mode" = "深色模式";
"follow_system" = "跟随系统";

// 语言设置
"app_language" = "应用语言";
"system_language_settings" = "前往系统设置";

// 播放设置
"auto_play" = "自动播放";
"auto_play_desc" = "打开应用时自动恢复上次播放的混音";
"background_play" = "后台播放";
"background_play_desc" = "切换到其他应用时继续播放声音";
"lock_screen_control" = "锁屏控制";
"lock_screen_control_desc" = "无需打开应用即可在锁屏和控制中心直接控制播放";

// Lock Screen Control VIP
"lock_screen_control_title" = "锁屏控制";
"lock_screen_control_vip_desc" = "升级到VIP以在锁屏和控制中心控制播放";
"lock_screen_control_feature" = "锁屏控制";
"lock_screen_control_feature_desc" = "在锁屏和控制中心控制播放";
"lock_screen_control_benefit1" = "无需解锁设备即可控制播放";
"lock_screen_control_benefit2" = "从控制中心快速访问";
"lock_screen_control_benefit3" = "与iOS媒体控制无缝集成";

// Auto Play Messages
"auto_play_enabled" = "自动播放已开启";
"auto_play_disabled" = "自动播放已关闭";
"no_last_mix_found" = "未找到上次播放的混音";
"last_mix_loaded" = "已加载上次播放的混音";
"auto_play_mix_name" = "自动播放混音";
"auto_play_mix_desc" = "为自动播放功能保存的混音";

// Background Play Messages
"background_play_enabled" = "后台播放已开启";
"background_play_disabled" = "后台播放已关闭";

// 关于
"version" = "版本";
"feedback" = "反馈";
"feedback_desc" = "发送反馈和建议";
"rate_app" = "评分";
"rate_app_desc" = "在App Store中评分";

// MARK: - Timer
"timer" = "定时器";
"timer_remaining" = "剩余 %@";
"set_timer" = "设置定时器";
"timer_minutes" = "分钟";
"timer_hours" = "小时";

// MARK: - Alerts & Messages
"error" = "错误";
"success" = "成功";
"loading" = "加载中...";
"saved_successfully" = "保存成功";
"deleted_successfully" = "删除成功";

// MARK: - Accessibility
"play_button" = "播放按钮";
"pause_button" = "暂停按钮";
"volume_slider" = "音量滑块";
"close_button" = "关闭按钮";

// MARK: - VIP Subscription
// VIP Feature Titles
"vip_sound_title" = "VIP 专属声音";
"mix_limit_title" = "无限制混音";
"save_limit_title" = "无限制收藏";
"vip_theme_title" = "VIP 专属主题";
"long_timer_title" = "长时间定时器";

// VIP Feature Descriptions
"vip_sound_desc" = "此声音需要 VIP 会员才能使用。升级到 VIP 解锁所有高品质专属声音！";
"mix_limit_desc" = "免费用户最多只能同时播放 %d 个声音。升级到 VIP 享受无限制混音体验！";
"save_limit_desc" = "免费用户最多只能保存 %d 个收藏。升级到 VIP 享受无限制收藏！";
"vip_theme_desc" = "此主题需要 VIP 会员才能使用。升级到 VIP 解锁所有精美主题！";
"long_timer_desc" = "免费用户只能设置最长 30 分钟的定时器。升级到 VIP 以享受更长时间的定时播放！";

// VIP Benefits
"upgrade_will_get" = "升级后您将获得：";
"all_vip_benefits" = "VIP 会员全部权益";

// VIP Sound Benefits
"vip_sound_benefit_1" = "解锁所有 VIP 专属声音";
"vip_sound_benefit_2" = "高品质音频体验";
"vip_sound_benefit_3" = "独家声音内容";

// Mix Limit Benefits
"mix_limit_benefit_1" = "无限制添加声音到混音";
"mix_limit_benefit_2" = "创建复杂的声音组合";
"mix_limit_benefit_3" = "专业级混音体验";

// Save Limit Benefits
"save_limit_benefit_1" = "保存无限数量的收藏";
"save_limit_benefit_2" = "创建个人声音库";
"save_limit_benefit_3" = "随时访问喜爱的混音";

// VIP Theme Benefits
"vip_theme_benefit_1" = "解锁所有精美主题";
"vip_theme_benefit_2" = "个性化界面体验";
"vip_theme_benefit_3" = "独家视觉设计";



// Long Timer Benefits
"long_timer_benefit_1" = "设置超过 30 分钟的定时器";
"long_timer_benefit_2" = "延长放松时间";
"long_timer_benefit_3" = "完美适合睡眠和冥想";

// VIP Feature List (for all features view)
"vip_sound_feature" = "VIP 专属声音";
"vip_sound_feature_desc" = "解锁高品质专属声音";
"mix_limit_feature" = "无限制混音";
"mix_limit_feature_desc" = "混音中添加无限声音";
"save_limit_feature" = "无限制收藏";
"save_limit_feature_desc" = "保存无限数量的收藏";
"vip_theme_feature" = "VIP 专属主题";
"vip_theme_feature_desc" = "使用所有精美主题";
"long_timer_feature" = "长时间定时器";
"long_timer_feature_desc" = "设置超过 30 分钟的定时器";

// Action Buttons
"upgrade_to_vip" = "升级到 VIP";
"later_button" = "稍后再说";
"tap_to_upgrade" = "点击升级";

// MARK: - Theme Names
"theme_ocean_breeze" = "海洋微风";
"theme_sunset_glow" = "夕阳余晖";
"theme_forest_mist" = "森林薄雾";
"theme_night_sky" = "夜空星辰";
"theme_lavender_dream" = "薰衣草梦境";
"theme_fire_ember" = "火焰余烬";

// MARK: - Theme Selector
"select_theme" = "选择使用";
"current_theme" = "当前: %@";
"theme_in_use" = "使用中";
"previous_theme" = "上一个";
"next_theme" = "下一个";

// 主题确认
"confirm_theme_change" = "确认更换主题";
"confirm_theme_change_message" = "确定要切换到「%@」主题吗？";
"confirm" = "确认";

// 删除收藏确认
"delete_favorite_confirm" = "删除收藏混音";
"delete_favorite_message" = "删除最后一个声音将同时删除收藏混音「%@」，确定要继续吗？";
"delete_favorite" = "删除收藏";

// MARK: - Timer
"timer" = "定时";
"time_remaining" = "剩余时间";
"set_timer" = "设置定时器";
"timer_description" = "选择播放时长，时间到后自动停止";
"quick_select" = "快速选择";
"custom_time" = "自定义时间";
"hours" = "小时";
"minutes" = "分钟";
"seconds" = "秒";
"start_timer" = "开始定时";
"cancel_timer" = "取消定时";
"elapsed_time" = "已播放 %@";

// Timer durations
"timer_15min" = "15分钟";
"timer_30min" = "30分钟";
"timer_45min" = "45分钟";
"timer_1hour" = "1小时";
"timer_1_5hour" = "1.5小时";
"timer_2hour" = "2小时";

// Now Playing Info
"playing_count" = "正在播放 %d / %d 个声音";
"active_sounds_count" = "%d 个活跃声音";
"app_subtitle" = "环境声音混音器";

// MARK: - Subscription Plans
"monthly_subscription" = "月度订阅";
"yearly_subscription" = "年度订阅";
"lifetime_purchase" = "终生购买";
"popular_badge" = "热门";
"best_value_badge" = "最超值";
"one_time_purchase" = "一次性购买";
"cancel_anytime" = "随时可以取消";
"save_over_30_percent" = "相比月度订阅节省超过30%";
"lifetime_description" = "一次购买，永久享受所有VIP功能";
"choose_subscription_plan" = "选择订阅计划";
"loading_subscription_options" = "正在加载订阅选项...";
"restore_purchases" = "恢复购买";
"terms_of_service" = "使用条款";
"privacy_policy" = "隐私政策";
"subscription_success" = "订阅成功！感谢您的支持！";
"purchase_failed" = "购买失败：%@";
"restore_success" = "购买已恢复！";
"no_purchases_to_restore" = "没有找到可恢复的购买记录";
"processing" = "处理中...";

// MARK: - Subscription Interface
"upgrade_to_vip_title" = "升级到 VIP";
"upgrade_subtitle" = "解锁所有高级功能，享受完整的DIY Noise体验";
"vip_membership_benefits" = "VIP 会员特权";
"preview_mode_notice" = "预览模式 - 请配置App Store Connect产品";

// Subscription Period
"daily" = "每日";
"weekly" = "每周";
"monthly" = "每月";
"yearly" = "每年";

// VIP Features
"vip_exclusive_sounds" = "VIP 专属声音";
"vip_exclusive_sounds_desc" = "解锁所有高品质专属声音";
"unlimited_mixing" = "无限制混音";
"unlimited_mixing_desc" = "混音中添加无限数量的声音";
"unlimited_favorites" = "无限制收藏";
"unlimited_favorites_desc" = "保存无限数量的收藏混音";
"vip_exclusive_themes" = "VIP 专属主题";
"vip_exclusive_themes_desc" = "使用所有精美的界面主题";
"lock_screen_control_feature" = "锁屏控制";
"lock_screen_control_feature_desc" = "在锁屏和控制中心直接控制播放";
"extended_timer" = "长时间定时器";
"extended_timer_desc" = "设置超过30分钟的定时播放";

// Alert
"alert_title" = "提示";
"ok_button" = "确定";

import SwiftUI
import Combine
import CloudKit
import StoreKit

// MARK: - Store Errors
enum StoreError: Error {
    case failedVerification
    case productNotFound
    case purchaseFailed

    var localizedDescription: String {
        switch self {
        case .failedVerification:
            return "交易验证失败"
        case .productNotFound:
            return "产品未找到"
        case .purchaseFailed:
            return "购买失败"
        }
    }
}

// MARK: - Subscription Manager
class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()
    
    @Published var isSubscribed: Bool = false
    @Published var isCloudSignedIn: Bool = false
    @Published var cloudUserName: String = ""
    @Published var availableProducts: [Product] = []
    @Published var purchasedSubscriptions: [Product] = []
    @Published var subscriptionStatus: Product.SubscriptionInfo.Status?

    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()
    private var updateListenerTask: Task<Void, Error>?
    
    // UserDefaults Keys
    private let subscriptionKey = "isSubscribed"
    private let cloudSignInKey = "isCloudSignedIn"
    private let cloudUserNameKey = "cloudUserName"

    // StoreKit Product IDs - 您需要在App Store Connect中配置这些产品
    enum ProductID {
        static let monthlyVIP = "monthly"
        static let yearlyVIP = "yearly"
        static let lifetimeVIP = "upgrade_to_vip"

        static var allCases: [String] {
            return [monthlyVIP, yearlyVIP, lifetimeVIP]
        }
    }
    
    // Subscription Limits
    struct Limits {
        static let maxMixSoundsForFree = 3      // 免费用户最多3个声音组合
        static let maxSavedMixesForFree = 5     // 免费用户最多5个收藏
        static let vipThemesStartIndex = 3      // 从第4个主题开始需要VIP
    }
    
    init() {
        loadSettings()
        setupObservers()
        checkCloudKitStatus()

        // 启动StoreKit监听器
        updateListenerTask = listenForTransactions()

        // 加载产品和检查订阅状态
        Task {
            await loadProducts()
            await updateSubscriptionStatus()
        }


    }

    deinit {
        updateListenerTask?.cancel()
    }
    
    private func loadSettings() {
        isSubscribed = userDefaults.bool(forKey: subscriptionKey)
        isCloudSignedIn = userDefaults.bool(forKey: cloudSignInKey)
        cloudUserName = userDefaults.string(forKey: cloudUserNameKey) ?? ""
    }
    
    private func setupObservers() {
        $isSubscribed
            .dropFirst()
            .sink { [weak self] value in
                self?.userDefaults.set(value, forKey: self?.subscriptionKey ?? "")
            }
            .store(in: &cancellables)
        
        $isCloudSignedIn
            .dropFirst()
            .sink { [weak self] value in
                self?.userDefaults.set(value, forKey: self?.cloudSignInKey ?? "")
            }
            .store(in: &cancellables)
        
        $cloudUserName
            .dropFirst()
            .sink { [weak self] value in
                self?.userDefaults.set(value, forKey: self?.cloudUserNameKey ?? "")
            }
            .store(in: &cancellables)
    }
    
    // MARK: - StoreKit 2 Integration

    @MainActor
    func loadProducts() async {
        do {
            let products = try await Product.products(for: ProductID.allCases)
            self.availableProducts = products.sorted { product1, product2 in
                // 自定义排序：月度 → 年度 → 终生
                let order1 = getProductOrder(product1)
                let order2 = getProductOrder(product2)
                return order1 < order2
            }

        } catch {

        }
    }

    private func getProductOrder(_ product: Product) -> Int {
        switch product.id {
        case ProductID.monthlyVIP:
            return 1
        case ProductID.yearlyVIP:
            return 2
        case ProductID.lifetimeVIP:
            return 3
        default:
            return 4
        }
    }

    func purchase(_ product: Product) async throws -> StoreKit.Transaction? {


        let result = try await product.purchase()

        switch result {
        case .success(let verification):
            let transaction = try checkVerified(verification)
            await updateSubscriptionStatus()
            await transaction.finish()
            return transaction

        case .userCancelled:
            return nil

        case .pending:
            return nil

        @unknown default:
            return nil
        }
    }

    func restorePurchases() async {

        do {
            try await AppStore.sync()
            await updateSubscriptionStatus()
        } catch {
        }
    }

    @MainActor
    func updateSubscriptionStatus() async {
        var isCurrentlySubscribed = false
        var currentSubscriptions: [Product] = []

        for await result in StoreKit.Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)

                if let product = availableProducts.first(where: { $0.id == transaction.productID }) {
                    currentSubscriptions.append(product)
                    isCurrentlySubscribed = true

                    // 获取订阅状态详情（仅对订阅类型产品）
                    if let subscription = product.subscription {
                        let statuses = try await subscription.status
                        self.subscriptionStatus = statuses.first
                    }


                }
            } catch {

            }
        }

        self.purchasedSubscriptions = currentSubscriptions
        self.isSubscribed = isCurrentlySubscribed


    }

    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in StoreKit.Transaction.updates {
                do {
                    let transaction = try self.checkVerified(result)
                    await self.updateSubscriptionStatus()
                    await transaction.finish()
                } catch {

                }
            }
        }
    }

    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    // MARK: - Legacy Subscription Management (for testing)
    func subscribe() {
        // 用于测试的简单订阅方法
        isSubscribed = true
    }

    func unsubscribe() {
        isSubscribed = false
    }
    
    // MARK: - Feature Access Control
    func canUseVIPSound() -> Bool {
        return isSubscribed
    }
    
    func canAddMoreSoundsToMix(currentCount: Int) -> Bool {
        if isSubscribed {
            return true
        }
        return currentCount < Limits.maxMixSoundsForFree
    }
    
    func canSaveMoreMixes(currentCount: Int) -> Bool {
        if isSubscribed {
            return true
        }
        return currentCount < Limits.maxSavedMixesForFree
    }
    
    func canUseTheme(themeIndex: Int) -> Bool {
        if isSubscribed {
            return true
        }
        return themeIndex < Limits.vipThemesStartIndex
    }
    
    func canUseBackgroundPlay() -> Bool {
        return true  // 后台播放现在是免费功能
    }
    
    // MARK: - CloudKit Integration
    private func checkCloudKitStatus() {
        // 暂时禁用 CloudKit 检查，避免权限问题
        // 在实际发布时需要配置 CloudKit 容器和权限
        DispatchQueue.main.async { [weak self] in
            self?.isCloudSignedIn = false
            self?.cloudUserName = ""
        }

        // TODO: 在配置好 CloudKit 后启用以下代码
        /*
        CKContainer.default().accountStatus { [weak self] status, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.isCloudSignedIn = false
                    self?.cloudUserName = ""
                    return
                }

                switch status {
                case .available:
                    self?.fetchCloudUserInfo()
                case .noAccount, .restricted, .couldNotDetermine:
                    self?.isCloudSignedIn = false
                    self?.cloudUserName = ""
                case .temporarilyUnavailable:
                    self?.isCloudSignedIn = false
                    self?.cloudUserName = ""
                @unknown default:
                    self?.isCloudSignedIn = false
                    self?.cloudUserName = ""
                }
            }
        }
        */
    }
    
    private func fetchCloudUserInfo() {
        // 暂时禁用用户信息获取
        DispatchQueue.main.async { [weak self] in
            self?.isCloudSignedIn = false
            self?.cloudUserName = ""
        }

        // TODO: 在配置好 CloudKit 后启用以下代码
        /*
        CKContainer.default().fetchUserRecordID { [weak self] recordID, error in
            if let error = error {
                DispatchQueue.main.async {
                    self?.isCloudSignedIn = false
                    self?.cloudUserName = ""
                }
                return
            }

            if let recordID = recordID {
                // 注意：discoverUserIdentity 在 iOS 17+ 已被弃用
                // 需要使用新的 API 或其他方法获取用户信息
                DispatchQueue.main.async {
                    self?.isCloudSignedIn = true
                    self?.cloudUserName = "iCloud User"
                }
            } else {
                DispatchQueue.main.async {
                    self?.isCloudSignedIn = false
                    self?.cloudUserName = ""
                }
            }
        }
        */
    }
    
    // MARK: - Cloud Sync
    func syncMixesToCloud(_ mixes: [MixPreset]) {
        guard isCloudSignedIn else {
            return
        }

        // TODO: 实现 CloudKit 同步逻辑
        // 需要配置 CloudKit 容器和数据模型
    }

    func fetchMixesFromCloud(completion: @escaping ([MixPreset]) -> Void) {
        guard isCloudSignedIn else {
            completion([])
            return
        }

        // TODO: 实现从 CloudKit 获取数据的逻辑
        // 需要配置 CloudKit 容器和数据模型
        completion([])
    }
}

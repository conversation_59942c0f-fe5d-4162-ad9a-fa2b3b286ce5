import Foundation
import SwiftUI

// MARK: - Language Model
enum AppLanguage: String, CaseIterable, Codable {
    case chinese = "zh-Hans"
    case english = "en"
    case japanese = "ja"
    case korean = "ko"
    case french = "fr"
    case spanish = "es"
    case italian = "it"
    case german = "de"
    case chinese_tradition = "zh-Hant"

    var displayName: String {
        switch self {
        case .chinese: return "中文"
        case .english: return "English"
        case .japanese: return "日本語"
        case .korean: return "한국어"
        case .french: return "Français"
        case .spanish: return "Español"
        case .italian: return "Italiano"
        case .german: return "German"
        case .chinese_tradition: return "繁体"
        }
    }

    var code: String {
        return self.rawValue
    }
}

// MARK: - Language Manager (Simplified)
class LanguageManager: ObservableObject {
    static let shared = LanguageManager()

    @Published var currentLanguage: AppLanguage

    init() {
        // Detect system language
        _ = Locale.current.language.languageCode?.identifier ?? "en"
        let fullLocale = Locale.current.identifier

        // Use full locale identifier for better detection
        let detectedLanguage = Self.detectLanguage(from: fullLocale)
        self.currentLanguage = detectedLanguage

        // Listen for system language changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(systemLanguageChanged),
            name: NSLocale.currentLocaleDidChangeNotification,
            object: nil
        )
    }

    @objc private func systemLanguageChanged() {
        _ = Locale.current.language.languageCode?.identifier ?? "en"
        let fullLocale = Locale.current.identifier

        let newLanguage = Self.detectLanguage(from: fullLocale)

        if newLanguage != currentLanguage {
            currentLanguage = newLanguage
            // Post notification for UI updates
            NotificationCenter.default.post(name: .languageChanged, object: nil)
        }
    }

    private static func detectLanguage(from languageCode: String) -> AppLanguage {
        switch languageCode {
        // 繁体中文的各种可能格式
        case let code where code.contains("zh-Hant") || code.contains("zh_Hant") || code.contains("zh-TW") || code.contains("zh_TW") || code.contains("zh-HK") || code.contains("zh_HK"):
            return .chinese_tradition
        // 简体中文
        case let code where code.contains("zh-Hans") || code.contains("zh_Hans") || code.contains("zh-CN") || code.contains("zh_CN") || code.hasPrefix("zh"):
            return .chinese
        case let code where code.hasPrefix("ja"):
            return .japanese
        case let code where code.hasPrefix("ko"):
            return .korean
        case let code where code.hasPrefix("fr"):
            return .french
        case let code where code.hasPrefix("de"):
            return .german
        case let code where code.hasPrefix("it"):
            return .italian
        case let code where code.hasPrefix("es"):
            return .spanish
        default:
            return .english
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Notification Extension
extension Notification.Name {
    static let languageChanged = Notification.Name("languageChanged")
}

// MARK: - Localized String Helper with Language Manager
extension String {
    var localizedWithManager: String {
        let language = LanguageManager.shared.currentLanguage
        
        // Get the bundle for the specific language
        guard let path = Bundle.main.path(forResource: language.rawValue, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            return NSLocalizedString(self, comment: "")
        }
        
        return NSLocalizedString(self, bundle: bundle, comment: "")
    }
}

// MARK: - Updated L10n with Language Manager
struct L10nManager {
    // MARK: - App
    static var appName: String { "app_name".localizedWithManager }
    static var appSubtitle: String { "app_subtitle".localizedWithManager }
    
    // MARK: - Tab Bar
    static var tabSounds: String { "tab_sounds".localizedWithManager }
    static var tabFavorites: String { "tab_favorites".localizedWithManager }
    static var tabSettings: String { "tab_settings".localizedWithManager }
    
    // MARK: - UI Elements
    static func soundCount(_ count: Int) -> String {
        return String(format: "sound_count".localizedWithManager, count)
    }
    
    static func playingCount(_ playing: Int, _ total: Int) -> String {
        return String(format: "playing_count".localizedWithManager, playing, total)
    }
    
    static var volume: String { "volume".localizedWithManager }
    
    // MARK: - Buttons
    static var play: String { "play".localizedWithManager }
    static var pause: String { "pause".localizedWithManager }
    static var stop: String { "stop".localizedWithManager }
    static var stopAll: String { "stop_all".localizedWithManager }
    static var save: String { "save".localizedWithManager }
    static var cancel: String { "cancel".localizedWithManager }
    static var delete: String { "delete".localizedWithManager }
    static var edit: String { "edit".localizedWithManager }
    static var done: String { "done".localizedWithManager }
    static var close: String { "close".localizedWithManager }
    static var back: String { "back".localizedWithManager }
    
    // MARK: - Mixer
    static var mixerTitle: String { "mixer_title".localizedWithManager }
    static func activeSoundsCount(_ count: Int) -> String {
        return String(format: "active_sounds_count".localizedWithManager, count)
    }
    static var noActiveSounds: String { "no_active_sounds".localizedWithManager }
    static var noActiveSoundsDesc: String { "no_active_sounds_desc".localizedWithManager }
    static var saveMix: String { "save_mix".localizedWithManager }
    static var saveAsNewMix: String { "save_as_new_mix".localizedWithManager }
    static var updateCurrentMix: String { "update_current_mix".localizedWithManager }
    static var updateAndAddSounds: String { "update_and_add_sounds".localizedWithManager }
    static var updateMix: String { "update_mix".localizedWithManager }
    static var updateMixPrompt: String { "update_mix_prompt".localizedWithManager }
    static var savedMixes: String { "saved_mixes".localizedWithManager }
    static var mixName: String { "mix_name".localizedWithManager }
    static var mixDescription: String { "mix_description".localizedWithManager }
    static var mixNamePlaceholder: String { "mix_name_placeholder".localizedWithManager }
    static var mixDescriptionPlaceholder: String { "mix_description_placeholder".localizedWithManager }
    static var mixNamePrompt: String { "mix_name_prompt".localizedWithManager }
    static var unnamedMix: String { "unnamed_mix".localizedWithManager }
    
    // MARK: - Favorites
    static var favoritesTitle: String { "favorites_title".localizedWithManager }
    static var favoritesSubtitle: String { "favorites_subtitle".localizedWithManager }
    static var favoritesCount: String { "favorites_count".localizedWithManager }
    static var noFavorites: String { "no_favorites".localizedWithManager }
    static var noFavoritesDesc: String { "no_favorites_desc".localizedWithManager }
    static var deleteFavorite: String { "delete_favorite".localizedWithManager }
    static var deleteFavoriteMessage: String { "delete_favorite_message".localizedWithManager }
    static func mixSoundsCount(_ count: Int) -> String {
        return String(format: "mix_sounds_count".localizedWithManager, count)
    }
    static var mixCombination: String { "mix_combination".localizedWithManager }
    
    // MARK: - Settings
    static var settingsTitle: String { "settings_title".localizedWithManager }
    static var settingsSubtitle: String { "settings_subtitle".localizedWithManager }
    static var appearanceSection: String { "appearance_section".localizedWithManager }
    static var languageSection: String { "language_section".localizedWithManager }
    static var playbackSection: String { "playback_section".localizedWithManager }
    static var aboutSection: String { "about_section".localizedWithManager }

    // 外观设置
    static var themeColor: String { "theme_color".localizedWithManager }

    // 语言设置
    static var appLanguage: String { "app_language".localizedWithManager }
    static var systemLanguageSettings: String { "system_language_settings".localizedWithManager }

    // 播放设置
    static var autoPlay: String { "auto_play".localizedWithManager }
    static var autoPlayDesc: String { "auto_play_desc".localizedWithManager }
    static var backgroundPlay: String { "background_play".localizedWithManager }
    static var backgroundPlayDesc: String { "background_play_desc".localizedWithManager }
    static var lockScreenControl: String { "lock_screen_control".localizedWithManager }
    static var lockScreenControlDesc: String { "lock_screen_control_desc".localizedWithManager }

    // Lock Screen Control VIP
    static var lockScreenControlTitle: String { "lock_screen_control_title".localizedWithManager }
    static var lockScreenControlVipDesc: String { "lock_screen_control_vip_desc".localizedWithManager }
    static var lockScreenControlFeature: String { "lock_screen_control_feature".localizedWithManager }
    static var lockScreenControlFeatureDesc: String { "lock_screen_control_feature_desc".localizedWithManager }
    static var lockScreenControlBenefit1: String { "lock_screen_control_benefit1".localizedWithManager }
    static var lockScreenControlBenefit2: String { "lock_screen_control_benefit2".localizedWithManager }
    static var lockScreenControlBenefit3: String { "lock_screen_control_benefit3".localizedWithManager }

    // Auto Play Messages
    static var autoPlayEnabled: String { "auto_play_enabled".localizedWithManager }
    static var autoPlayDisabled: String { "auto_play_disabled".localizedWithManager }
    static var noLastMixFound: String { "no_last_mix_found".localizedWithManager }
    static var lastMixLoaded: String { "last_mix_loaded".localizedWithManager }
    static var autoPlayMixName: String { "auto_play_mix_name".localizedWithManager }
    static var autoPlayMixDesc: String { "auto_play_mix_desc".localizedWithManager }

    // Background Play Messages
    static var backgroundPlayEnabled: String { "background_play_enabled".localizedWithManager }
    static var backgroundPlayDisabled: String { "background_play_disabled".localizedWithManager }
    static var backgroundPlayVipRequired: String { "background_play_vip_required".localizedWithManager }

    // 关于
    static var version: String { "version".localizedWithManager }
    static var feedback: String { "feedback".localizedWithManager }
    static var feedbackDesc: String { "feedback_desc".localizedWithManager }
    static var rateApp: String { "rate_app".localizedWithManager }
    static var rateAppDesc: String { "rate_app_desc".localizedWithManager }

    // MARK: - VIP Subscription
    // VIP Feature Titles
    static var vipSoundTitle: String { "vip_sound_title".localizedWithManager }
    static var mixLimitTitle: String { "mix_limit_title".localizedWithManager }
    static var saveLimitTitle: String { "save_limit_title".localizedWithManager }
    static var vipThemeTitle: String { "vip_theme_title".localizedWithManager }
    static var longTimerTitle: String { "long_timer_title".localizedWithManager }

    // VIP Feature Descriptions
    static var vipSoundDesc: String { "vip_sound_desc".localizedWithManager }
    static func mixLimitDesc(_ maxSounds: Int) -> String {
        return String(format: "mix_limit_desc".localizedWithManager, maxSounds)
    }
    static func saveLimitDesc(_ maxMixes: Int) -> String {
        return String(format: "save_limit_desc".localizedWithManager, maxMixes)
    }
    static var vipThemeDesc: String { "vip_theme_desc".localizedWithManager }
    static var longTimerDesc: String { "long_timer_desc".localizedWithManager }

    // VIP Benefits
    static var upgradeWillGet: String { "upgrade_will_get".localizedWithManager }
    static var allVipBenefits: String { "all_vip_benefits".localizedWithManager }

    // VIP Sound Benefits
    static var vipSoundBenefit1: String { "vip_sound_benefit_1".localizedWithManager }
    static var vipSoundBenefit2: String { "vip_sound_benefit_2".localizedWithManager }
    static var vipSoundBenefit3: String { "vip_sound_benefit_3".localizedWithManager }

    // Mix Limit Benefits
    static var mixLimitBenefit1: String { "mix_limit_benefit_1".localizedWithManager }
    static var mixLimitBenefit2: String { "mix_limit_benefit_2".localizedWithManager }
    static var mixLimitBenefit3: String { "mix_limit_benefit_3".localizedWithManager }

    // Save Limit Benefits
    static var saveLimitBenefit1: String { "save_limit_benefit_1".localizedWithManager }
    static var saveLimitBenefit2: String { "save_limit_benefit_2".localizedWithManager }
    static var saveLimitBenefit3: String { "save_limit_benefit_3".localizedWithManager }

    // VIP Theme Benefits
    static var vipThemeBenefit1: String { "vip_theme_benefit_1".localizedWithManager }
    static var vipThemeBenefit2: String { "vip_theme_benefit_2".localizedWithManager }
    static var vipThemeBenefit3: String { "vip_theme_benefit_3".localizedWithManager }



    // Long Timer Benefits
    static var longTimerBenefit1: String { "long_timer_benefit_1".localizedWithManager }
    static var longTimerBenefit2: String { "long_timer_benefit_2".localizedWithManager }
    static var longTimerBenefit3: String { "long_timer_benefit_3".localizedWithManager }

    // VIP Feature List (for all features view)
    static var vipSoundFeature: String { "vip_sound_feature".localizedWithManager }
    static var vipSoundFeatureDesc: String { "vip_sound_feature_desc".localizedWithManager }
    static var mixLimitFeature: String { "mix_limit_feature".localizedWithManager }
    static var mixLimitFeatureDesc: String { "mix_limit_feature_desc".localizedWithManager }
    static var saveLimitFeature: String { "save_limit_feature".localizedWithManager }
    static var saveLimitFeatureDesc: String { "save_limit_feature_desc".localizedWithManager }
    static var vipThemeFeature: String { "vip_theme_feature".localizedWithManager }
    static var vipThemeFeatureDesc: String { "vip_theme_feature_desc".localizedWithManager }
    static var longTimerFeature: String { "long_timer_feature".localizedWithManager }
    static var longTimerFeatureDesc: String { "long_timer_feature_desc".localizedWithManager }

    // MARK: - Subscription Interface
    static var upgradeToVipTitle: String { "upgrade_to_vip_title".localizedWithManager }
    static var upgradeSubtitle: String { "upgrade_subtitle".localizedWithManager }
    static var vipMembershipBenefits: String { "vip_membership_benefits".localizedWithManager }
    static var previewModeNotice: String { "preview_mode_notice".localizedWithManager }
    static var chooseSubscriptionPlan: String { "choose_subscription_plan".localizedWithManager }
    static var loadingSubscriptionOptions: String { "loading_subscription_options".localizedWithManager }

    // Subscription Period
    static var daily: String { "daily".localizedWithManager }
    static var weekly: String { "weekly".localizedWithManager }
    static var monthly: String { "monthly".localizedWithManager }
    static var yearly: String { "yearly".localizedWithManager }

    // VIP Features
    static var vipExclusiveSounds: String { "vip_exclusive_sounds".localizedWithManager }
    static var vipExclusiveSoundsDesc: String { "vip_exclusive_sounds_desc".localizedWithManager }
    static var unlimitedMixing: String { "unlimited_mixing".localizedWithManager }
    static var unlimitedMixingDesc: String { "unlimited_mixing_desc".localizedWithManager }
    static var unlimitedFavorites: String { "unlimited_favorites".localizedWithManager }
    static var unlimitedFavoritesDesc: String { "unlimited_favorites_desc".localizedWithManager }
    static var vipExclusiveThemes: String { "vip_exclusive_themes".localizedWithManager }
    static var vipExclusiveThemesDesc: String { "vip_exclusive_themes_desc".localizedWithManager }
    static var extendedTimer: String { "extended_timer".localizedWithManager }
    static var extendedTimerDesc: String { "extended_timer_desc".localizedWithManager }

    // Alert
    static var alertTitle: String { "alert_title".localizedWithManager }
    static var okButton: String { "ok_button".localizedWithManager }





    // Subscription Plans
    static var bestValueBadge: String { "best_value_badge".localizedWithManager }
    static var popularBadge: String { "popular_badge".localizedWithManager }
    static var oneTimePurchase: String { "one_time_purchase".localizedWithManager }
    static var cancelAnytime: String { "cancel_anytime".localizedWithManager }
    static var saveOver30Percent: String { "save_over_30_percent".localizedWithManager }
    static var lifetimeDescription: String { "lifetime_description".localizedWithManager }
    static var restorePurchases: String { "restore_purchases".localizedWithManager }
    static var termsOfService: String { "terms_of_service".localizedWithManager }
    static var privacyPolicy: String { "privacy_policy".localizedWithManager }
    static var subscriptionSuccess: String { "subscription_success".localizedWithManager }
    static var purchaseFailed: String { "purchase_failed".localizedWithManager }
    static var restoreSuccess: String { "restore_success".localizedWithManager }
    static var noPurchasesToRestore: String { "no_purchases_to_restore".localizedWithManager }



    // Action Buttons
    static var upgradeToVip: String { "upgrade_to_vip".localizedWithManager }
    static var laterButton: String { "later_button".localizedWithManager }
    static var tapToUpgrade: String { "tap_to_upgrade".localizedWithManager }

    // MARK: - Theme Names
    static var themeOceanBreeze: String { "theme_ocean_breeze".localizedWithManager }
    static var themeSunsetGlow: String { "theme_sunset_glow".localizedWithManager }
    static var themeForestMist: String { "theme_forest_mist".localizedWithManager }
    static var themeNightSky: String { "theme_night_sky".localizedWithManager }
    static var themeLavenderDream: String { "theme_lavender_dream".localizedWithManager }
    static var themeFireEmber: String { "theme_fire_ember".localizedWithManager }

    // MARK: - Theme Selector
    static var selectTheme: String { "select_theme".localizedWithManager }
    static func currentTheme(_ themeName: String) -> String {
        return String(format: "current_theme".localizedWithManager, themeName)
    }
    static var themeInUse: String { "theme_in_use".localizedWithManager }
    static var previousTheme: String { "previous_theme".localizedWithManager }
    static var nextTheme: String { "next_theme".localizedWithManager }

    // 主题确认
    static var confirmThemeChange: String { "confirm_theme_change".localizedWithManager }
    static func confirmThemeChangeMessage(_ themeName: String) -> String {
        String(format: "confirm_theme_change_message".localizedWithManager, themeName)
    }
    static var confirm: String { "confirm".localizedWithManager }

    // 删除收藏确认
    static var deleteFavoriteConfirm: String { "delete_favorite_confirm".localizedWithManager }
    static func deleteFavoriteMessage(_ mixName: String) -> String {
        String(format: "delete_favorite_message".localizedWithManager, mixName)
    }

    // MARK: - Timer
    static var timer: String { "timer".localizedWithManager }
    static var timeRemaining: String { "time_remaining".localizedWithManager }
    static var setTimer: String { "set_timer".localizedWithManager }
    static var timerDescription: String { "timer_description".localizedWithManager }
    static var quickSelect: String { "quick_select".localizedWithManager }
    static var customTime: String { "custom_time".localizedWithManager }
    static var hours: String { "hours".localizedWithManager }
    static var minutes: String { "minutes".localizedWithManager }
    static var seconds: String { "seconds".localizedWithManager }
    static var startTimer: String { "start_timer".localizedWithManager }
    static var cancelTimer: String { "cancel_timer".localizedWithManager }
    static func elapsedTime(_ time: String) -> String {
        return String(format: "elapsed_time".localizedWithManager, time)
    }

    // Timer durations
    static var timer15min: String { "timer_15min".localizedWithManager }
    static var timer30min: String { "timer_30min".localizedWithManager }
    static var timer45min: String { "timer_45min".localizedWithManager }
    static var timer1hour: String { "timer_1hour".localizedWithManager }
    static var timer1_5hour: String { "timer_1_5hour".localizedWithManager }
    static var timer2hour: String { "timer_2hour".localizedWithManager }
}

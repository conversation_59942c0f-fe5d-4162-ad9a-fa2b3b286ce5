import SwiftUI
import Combine

class ThemeManager: ObservableObject {
    static let shared = ThemeManager()

    @Published var currentTheme: AppTheme
    @Published var availableThemes: [AppTheme]

    private let userDefaults = UserDefaults.standard
    private let themeKey = "selectedTheme"
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        self.availableThemes = AppTheme.defaultThemes

        // Load saved theme or use default
        if let savedThemeData = userDefaults.data(forKey: themeKey),
           let savedTheme = try? JSONDecoder().decode(AppTheme.self, from: savedThemeData) {
            self.currentTheme = savedTheme
        } else {
            self.currentTheme = AppTheme.defaultThemes[0] // Default to ocean theme
        }

        // 监听订阅状态变化，VIP 过期时自动切换主题
        setupSubscriptionListener()
    }
    
    func setTheme(_ theme: AppTheme) {
        withAnimation(.easeInOut(duration: 0.5)) {
            currentTheme = theme
        }
        saveCurrentTheme()
    }
    
    private func saveCurrentTheme() {
        if let encoded = try? JSONEncoder().encode(currentTheme) {
            userDefaults.set(encoded, forKey: themeKey)
        }
    }

    // 设置订阅状态监听
    private func setupSubscriptionListener() {
        SubscriptionManager.shared.$isSubscribed
            .sink { [weak self] isSubscribed in
                self?.handleSubscriptionChange(isSubscribed: isSubscribed)
            }
            .store(in: &cancellables)
    }

    // 处理订阅状态变化
    private func handleSubscriptionChange(isSubscribed: Bool) {
        // 如果 VIP 过期且当前使用的是 VIP 主题，则切换到第一个免费主题
        if !isSubscribed && currentTheme.isVIP {
            switchToFirstAvailableTheme()
        }
    }

    // 切换到第一个可用的主题（非 VIP 主题）
    private func switchToFirstAvailableTheme() {
        if let firstFreeTheme = availableThemes.first(where: { !$0.isVIP }) {
            setTheme(firstFreeTheme)
        }
    }

    // 公开方法：检查当前主题权限并在必要时切换
    func validateCurrentThemePermission() {
        if currentTheme.isVIP && !SubscriptionManager.shared.isSubscribed {
            switchToFirstAvailableTheme()
        }
    }
    
    func nextTheme() {
        guard let currentIndex = availableThemes.firstIndex(where: { $0.id == currentTheme.id }) else {
            return
        }
        
        let nextIndex = (currentIndex + 1) % availableThemes.count
        setTheme(availableThemes[nextIndex])
    }
    
    func previousTheme() {
        guard let currentIndex = availableThemes.firstIndex(where: { $0.id == currentTheme.id }) else {
            return
        }
        
        let previousIndex = currentIndex == 0 ? availableThemes.count - 1 : currentIndex - 1
        setTheme(availableThemes[previousIndex])
    }
    
    // MARK: - Theme Properties
    var primaryGradient: LinearGradient {
        currentTheme.primaryGradient.swiftUIGradient
    }
    
    var secondaryGradient: LinearGradient {
        currentTheme.secondaryGradient.swiftUIGradient
    }
    
    var accentColor: Color {
        currentTheme.accentColor.color
    }
    
    var textColor: Color {
        currentTheme.textColor.color
    }
    
    var cardBackgroundColor: Color {
        currentTheme.cardBackgroundColor.color
    }
}

// MARK: - Theme Environment Key
struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue: ThemeManager = ThemeManager.shared
}

extension EnvironmentValues {
    var themeManager: ThemeManager {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - View Extensions for Theme
extension View {
    func themedBackground() -> some View {
        self.background(
            ThemeManager.shared.primaryGradient
                .ignoresSafeArea()
        )
    }
    
    func themedCard() -> some View {
        self
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(ThemeManager.shared.cardBackgroundColor)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )
            )
    }
    
    func themedText() -> some View {
        self.foregroundColor(ThemeManager.shared.textColor)
    }
    
    func themedAccent() -> some View {
        self.foregroundColor(ThemeManager.shared.accentColor)
    }
}

// MARK: - Glassmorphism Modifier
struct GlassmorphismModifier: ViewModifier {
    let cornerRadius: CGFloat
    let opacity: Double
    
    func body(content: Content) -> some View {
        content
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(.ultraThinMaterial.opacity(opacity))
                    .background(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.3),
                                        Color.white.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
    }
}

extension View {
    func glassmorphism(cornerRadius: CGFloat = 16, opacity: Double = 0.8) -> some View {
        self.modifier(GlassmorphismModifier(cornerRadius: cornerRadius, opacity: opacity))
    }
}

// MARK: - Animated Gradient Background
struct AnimatedGradientBackground: View {
    @StateObject private var themeManager = ThemeManager.shared
    @State private var animateGradient = false
    
    var body: some View {
        LinearGradient(
            colors: [
                Color(hex: themeManager.currentTheme.primaryGradient.colors[0]),
                Color(hex: themeManager.currentTheme.primaryGradient.colors[1]),
                Color(hex: themeManager.currentTheme.secondaryGradient.colors[0])
            ],
            startPoint: animateGradient ? .topLeading : .bottomTrailing,
            endPoint: animateGradient ? .bottomTrailing : .topLeading
        )
        .ignoresSafeArea()
        .onAppear {
            withAnimation(.easeInOut(duration: 3).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
        .onChange(of: themeManager.currentTheme.id) {
            withAnimation(.easeInOut(duration: 0.5)) {
                animateGradient.toggle()
            }
        }
    }
}

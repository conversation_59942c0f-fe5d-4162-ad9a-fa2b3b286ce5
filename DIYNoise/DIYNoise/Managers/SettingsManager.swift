import SwiftUI
import Combine
import AVFoundation



// MARK: - Settings Manager
class SettingsManager: ObservableObject {
    static let shared = SettingsManager()

    @Published var autoPlay: Bool
    @Published var backgroundPlay: Bool
    @Published var lockScreenControl: Bool

    // 订阅管理器引用
    private let subscriptionManager = SubscriptionManager.shared
    
    private let userDefaults = UserDefaults.standard

    // UserDefaults Keys
    private let autoPlayKey = "autoPlay"
    private let backgroundPlayKey = "backgroundPlay"
    private let lockScreenControlKey = "lockScreenControl"
    
    init() {
        // Load settings
        self.autoPlay = userDefaults.bool(forKey: autoPlayKey)
        self.backgroundPlay = userDefaults.bool(forKey: backgroundPlayKey)
        self.lockScreenControl = userDefaults.bool(forKey: lockScreenControlKey)

        // Setup observers
        setupObservers()
    }
    
    private func setupObservers() {
        // Auto play observer
        $autoPlay
            .dropFirst()
            .sink { [weak self] value in
                guard let self = self else { return }
                self.userDefaults.set(value, forKey: self.autoPlayKey)

                if value {
                    // 保存当前播放状态作为下次启动的参考
                    self.saveCurrentMixForAutoPlay()
                } else {
                }
            }
            .store(in: &cancellables)
        
        // Background play observer
        $backgroundPlay
            .dropFirst()
            .sink { [weak self] value in
                guard let self = self else { return }

                // 后台播放现在是免费功能，直接保存设置
                self.userDefaults.set(value, forKey: self.backgroundPlayKey)

                // 重新配置音频会话以适应新设置
                AudioManager.shared.configureAudioSessionForCurrentSettings()

                if value {
                } else {

                    // 如果用户关闭了后台播放，且应用当前在后台，则暂停音频
                    let appState = UIApplication.shared.applicationState
                    if appState == .background && AudioManager.shared.isPlaying {

                        // 记录当前播放状态
                        let audioManager = AudioManager.shared
                        let playingStates = audioManager.activeSounds.mapValues { $0.isPlaying }
                        if let data = try? JSONEncoder().encode(playingStates) {
                            UserDefaults.standard.set(data, forKey: "pausedSoundsForBackground")
                        }
                        UserDefaults.standard.set(true, forKey: "wasPausedForBackground")

                        audioManager.pauseAllSounds()
                    }
                }

                // 更新后台播放状态记录（现在总是等于用户设置）
                UserDefaults.standard.set(value, forKey: "currentBackgroundPlayEnabled")
            }
            .store(in: &cancellables)

        // Lock screen control observer
        $lockScreenControl
            .dropFirst()
            .sink { [weak self] value in
                guard let self = self else { return }

                // 检查VIP权限
                if value && !SubscriptionManager.shared.isSubscribed {
                    DispatchQueue.main.async {
                        self.lockScreenControl = false
                    }
                    return
                }

                self.userDefaults.set(value, forKey: self.lockScreenControlKey)

                // 重新配置音频会话以适应新设置
                AudioManager.shared.configureAudioSessionForCurrentSettings()

                if value {
                    // 配置 Now Playing 信息和远程控制
                    AudioManager.shared.setupRemoteCommandCenter()
                } else {
                    // 禁用远程控制
                    AudioManager.shared.disableRemoteCommandCenter()
                }
            }
            .store(in: &cancellables)

    }
    
    private var cancellables = Set<AnyCancellable>()
    

    
    // MARK: - Audio Settings
    func resetToDefaults() {
        autoPlay = false
        backgroundPlay = false
        lockScreenControl = false
    }
    
    // MARK: - App Lifecycle
    func handleAppLaunch() {
        // 检查锁屏控制的VIP权限
        if lockScreenControl && !SubscriptionManager.shared.isSubscribed {
            lockScreenControl = false
        }

        // 配置音频会话（考虑后台播放和锁屏控制）
        AudioManager.shared.configureAudioSessionForCurrentSettings()

        // 初始化锁屏控制
        if lockScreenControl {
            AudioManager.shared.setupRemoteCommandCenter()
        } else {
        }

        if autoPlay {
            // Auto-play last mix if enabled
            loadLastMixIfNeeded()
        }
    }
    
    private func loadLastMixIfNeeded() {

        if let data = userDefaults.data(forKey: "lastPlayedMix"),
           let mix = try? JSONDecoder().decode(MixPreset.self, from: data) {
            let allSounds = SoundCategory.sampleCategories.flatMap { $0.sounds }
            AudioManager.shared.loadMix(mix, sounds: allSounds)
        } else {
        }
    }
    
    func saveLastPlayedMix(_ mix: MixPreset) {
        if let data = try? JSONEncoder().encode(mix) {
            userDefaults.set(data, forKey: "lastPlayedMix")
        }
    }

    // MARK: - Auto Play Support
    private func saveCurrentMixForAutoPlay() {
        // 获取当前播放的声音并创建混音预设
        let audioManager = AudioManager.shared
        let activeSounds = audioManager.activeSounds

        if !activeSounds.isEmpty {
            // 将当前播放的声音转换为 SoundMix 格式
            let soundMixes = activeSounds.map { (soundId, player) in
                SoundMix(soundId: soundId, volume: player.volume, isEnabled: true)
            }

            let mix = MixPreset(
                name: L10nManager.autoPlayMixName,
                description: L10nManager.autoPlayMixDesc,
                soundMixes: soundMixes
            )
            saveLastPlayedMix(mix)
        }
    }

    // MARK: - Background Play Support
    // 音频会话配置现在由 AudioManager 统一管理

    // MARK: - Feature Check
    func canToggleBackgroundPlay() -> Bool {
        return true  // 后台播放现在是免费功能
    }


}



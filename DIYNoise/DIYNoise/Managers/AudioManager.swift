import Foundation
import AVFoundation
import Combine
import MediaPlayer
import MediaPlayer

class AudioManager: ObservableObject {
    static let shared = AudioManager()

    @Published var activeSounds: [UUID: any SoundPlayerProtocol] = [:]
    @Published var isPlaying: Bool = false
    @Published var volumeUpdateTrigger = UUID() // 用于触发UI更新
    @Published var currentPlayingMix: MixPreset? = nil // 当前播放的收藏混音

    private let subscriptionManager = SubscriptionManager.shared
    
    private var audioSession: AVAudioSession
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        self.audioSession = AVAudioSession.sharedInstance()
        setupAudioSession()
        setupAudioSessionNotifications()
        setupRemoteCommandCenter()
    }
    
    private func setupAudioSession() {
        configureAudioSessionForCurrentSettings()
    }

    private func setupAudioSessionNotifications() {
        // 监听音频会话中断
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAudioSessionInterruption),
            name: AVAudioSession.interruptionNotification,
            object: audioSession
        )

        // 监听音频会话路由变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAudioSessionRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: audioSession
        )
    }

    @objc private func handleAudioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        switch type {
        case .began:
            // 中断开始时暂停所有音频
            pauseAllSounds()
        case .ended:
            // 中断结束时恢复音频配置
            configureAudioSessionForCurrentSettings()

            // 检查是否应该恢复播放
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    resumeAllSounds()
                }
            }
        @unknown default:
            break
        }
    }

    @objc private func handleAudioSessionRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }

        switch reason {
        case .newDeviceAvailable:
            break
        case .oldDeviceUnavailable:
            pauseAllSounds()
        default:
            break
        }
    }

    func setupRemoteCommandCenter() {
        let commandCenter = MPRemoteCommandCenter.shared()

        // 清除之前的目标
        commandCenter.playCommand.removeTarget(nil)
        commandCenter.pauseCommand.removeTarget(nil)
        commandCenter.togglePlayPauseCommand.removeTarget(nil)
        commandCenter.stopCommand.removeTarget(nil)

        // 启用播放命令
        commandCenter.playCommand.isEnabled = true
        commandCenter.playCommand.addTarget { [weak self] event in
            self?.resumeAllSounds()
            return .success
        }

        // 启用暂停命令
        commandCenter.pauseCommand.isEnabled = true
        commandCenter.pauseCommand.addTarget { [weak self] event in
            self?.pauseAllSounds()
            return .success
        }

        // 启用播放/暂停切换命令（这个很重要！）
        commandCenter.togglePlayPauseCommand.isEnabled = true
        commandCenter.togglePlayPauseCommand.addTarget { [weak self] event in
            guard let self = self else { return .commandFailed }

            if self.isPlaying {
                self.pauseAllSounds()
            } else {
                self.resumeAllSounds()
            }
            return .success
        }

        // 启用停止命令
        commandCenter.stopCommand.isEnabled = true
        commandCenter.stopCommand.addTarget { [weak self] event in
            self?.stopAllSounds()
            return .success
        }


        // 立即更新 Now Playing 信息
        DispatchQueue.main.async {
            self.updateNowPlayingInfo()
        }
    }

    func disableRemoteCommandCenter() {
        let commandCenter = MPRemoteCommandCenter.shared()

        // 禁用所有命令
        commandCenter.playCommand.isEnabled = false
        commandCenter.pauseCommand.isEnabled = false
        commandCenter.togglePlayPauseCommand.isEnabled = false
        commandCenter.stopCommand.isEnabled = false

        // 移除所有目标
        commandCenter.playCommand.removeTarget(nil)
        commandCenter.pauseCommand.removeTarget(nil)
        commandCenter.togglePlayPauseCommand.removeTarget(nil)
        commandCenter.stopCommand.removeTarget(nil)

        // 清除 Now Playing 信息
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nil

    }

    func updateNowPlayingInfo() {
        guard SettingsManager.shared.lockScreenControl && SubscriptionManager.shared.isSubscribed else {
            // 如果锁屏控制被禁用或用户不是VIP，清除 Now Playing 信息
            MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
            return
        }

        var nowPlayingInfo = [String: Any]()

        // 设置应用信息
        nowPlayingInfo[MPMediaItemPropertyTitle] = "DIY Noise"

        // 根据当前播放状态设置描述
        if isPlaying && !activeSounds.isEmpty {
            let activeCount = activeSounds.values.filter { $0.isPlaying }.count
            if let currentMix = currentPlayingMix {
                nowPlayingInfo[MPMediaItemPropertyArtist] = currentMix.name
                nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = L10nManager.playingCount(activeCount, activeSounds.count)
            } else {
                nowPlayingInfo[MPMediaItemPropertyArtist] = L10nManager.mixerTitle
                nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = L10nManager.activeSoundsCount(activeCount)
            }
        } else if !activeSounds.isEmpty {
            // 有声音但暂停状态
            nowPlayingInfo[MPMediaItemPropertyArtist] = L10nManager.appSubtitle
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = L10nManager.pause
        } else {
            // 没有声音时也设置基本信息
            nowPlayingInfo[MPMediaItemPropertyArtist] = L10nManager.appSubtitle
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = "准备播放环境声音"
        }

        // 设置播放状态
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlaying ? 1.0 : 0.0

        // 设置媒体类型
        nowPlayingInfo[MPNowPlayingInfoPropertyMediaType] = MPNowPlayingInfoMediaType.audio.rawValue

        // 只有在使用定时器时才设置播放时长
        let timerManager = TimerManager.shared
        if timerManager.isTimerActive {
            // 设置定时器的总时长和已播放时长
            nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = timerManager.totalTime
            nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = timerManager.totalTime - timerManager.timeRemaining
        } else {
            // 没有定时器时，不设置时长信息，表示连续播放
            // 不设置这些属性，让系统知道这是连续播放的内容
        }

        // 设置图片（可选，使用应用图标）
        if let appIcon = UIImage(named: "AppIcon") {
            nowPlayingInfo[MPMediaItemPropertyArtwork] = MPMediaItemArtwork(boundsSize: appIcon.size) { _ in
                return appIcon
            }
        }

        // 更新 Now Playing 信息
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo

        if TimerManager.shared.isTimerActive {
        }
    }

    // 调试方法：强制更新 Now Playing 信息
    func forceUpdateNowPlayingInfo() {

        // 重新配置音频会话
        configureAudioSessionForCurrentSettings()

        // 重新设置远程控制
        if SettingsManager.shared.lockScreenControl {
            setupRemoteCommandCenter()
        }

        // 更新 Now Playing 信息
        updateNowPlayingInfo()

        // 检查远程控制中心状态
        let commandCenter = MPRemoteCommandCenter.shared()


    }



    // 配置音频会话 - 支持后台播放和锁屏控制
    func configureAudioSessionForCurrentSettings() {
        do {
            // 根据后台播放和锁屏控制设置选择配置
            let backgroundPlayEnabled = SettingsManager.shared.backgroundPlay
            let lockScreenEnabled = SettingsManager.shared.lockScreenControl && SubscriptionManager.shared.isSubscribed

            let options: AVAudioSession.CategoryOptions
            if lockScreenEnabled {
                // 锁屏控制启用时，不使用 mixWithOthers，让我们的应用成为主要音频源
                options = []
            } else if backgroundPlayEnabled {
                // 只有后台播放启用时，可以与其他音频混合
                options = [.mixWithOthers]
            } else {
                // 都没启用时，使用混合模式但不强制后台播放
                options = [.mixWithOthers]
            }

            let currentCategory = audioSession.category
            let currentOptions = audioSession.categoryOptions

            // 只有当类别或选项不同时才重新配置
            if currentCategory != .playback || currentOptions != options {
                try audioSession.setCategory(.playback, mode: .default, options: options)
            } else {
            }

            // 根据后台播放设置决定是否激活音频会话
            if backgroundPlayEnabled || lockScreenEnabled || isPlaying {
                try audioSession.setActive(true)
            }

            // 启用远程控制事件接收
            UIApplication.shared.beginReceivingRemoteControlEvents()

        } catch {
        }
    }

    // MARK: - Playback Control
    private func shouldAllowPlayback() -> Bool {
        let appState = UIApplication.shared.applicationState
        let settingsManager = SettingsManager.shared
        let hasBackgroundPlayPermission = settingsManager.backgroundPlay && settingsManager.canToggleBackgroundPlay()


        // 如果应用在前台，总是允许播放
        if appState == .active {
            return true
        }

        // 如果应用在后台，只有有后台播放权限时才允许播放
        if appState == .background {
            return hasBackgroundPlayPermission
        }

        // 其他状态（如非活跃状态）允许播放
        return true
    }

    // MARK: - Sound Control
    func playSound(_ sound: SoundItem) -> Bool {

        // 检查是否允许播放（考虑后台播放权限）
        if !shouldAllowPlayback() {
            return false
        }

        // 直接播放声音时，保持当前播放的收藏混音状态
        // 这样混音器可以检测到内容变化并显示更新/保存按钮
        if currentPlayingMix != nil {
        }

        // 在播放前确保音频会话配置正确
        configureAudioSessionForCurrentSettings()

        // 如果启用了锁屏控制，确保远程控制已设置
        if SettingsManager.shared.lockScreenControl {
            setupRemoteCommandCenter()
        }

        // 注意：权限检查现在在 UI 层面进行，这里只负责实际播放

        // Check if audio file exists
        guard let url = Bundle.main.url(forResource: sound.fileName, withExtension: "m4a") else {

            // Create a mock player for UI demonstration
            let mockPlayer = MockSoundPlayer(soundId: sound.id, soundName: sound.name, volume: sound.volume)
            mockPlayer.delegate = self
            activeSounds[sound.id] = mockPlayer
            mockPlayer.play()
            updatePlayingState()
            return true
        }

        if let existingPlayer = activeSounds[sound.id] {
            existingPlayer.play()
        } else {
            let player = SoundPlayer(url: url, soundId: sound.id, soundName: sound.name, volume: sound.volume)
            player.delegate = self
            activeSounds[sound.id] = player
            player.play()
        }

        updatePlayingState()
        return true
    }
    
    func pauseSound(_ soundId: UUID) {
        activeSounds[soundId]?.pause()
        updatePlayingState()
    }
    
    func stopSound(_ soundId: UUID) {
        activeSounds[soundId]?.stop()
        activeSounds.removeValue(forKey: soundId)
        updatePlayingState()
    }
    
    func stopAllSounds() {
        activeSounds.values.forEach { $0.stop() }
        activeSounds.removeAll()
        // 清除当前播放的收藏混音
        if currentPlayingMix != nil {
            currentPlayingMix = nil
        }
        updatePlayingState()
    }

    func pauseAllSounds() {
        activeSounds.values.forEach { $0.pause() }
        updatePlayingState()
    }

    func resumeAllSounds() {
        activeSounds.values.forEach { $0.play() }
        updatePlayingState()
    }
    
    func setVolume(for soundId: UUID, volume: Float) {
        // 确保音量在有效范围内
        let clampedVolume = max(0.0, min(1.0, volume))
        activeSounds[soundId]?.setVolume(clampedVolume)

        // 触发UI更新
        DispatchQueue.main.async {
            self.volumeUpdateTrigger = UUID()
        }

        // 更新播放状态
        updatePlayingState()
    }
    

    
    private func updatePlayingState() {
        isPlaying = activeSounds.values.contains { $0.isPlaying }
        // 触发UI更新
        volumeUpdateTrigger = UUID()
        // 更新 Now Playing 信息
        updateNowPlayingInfo()
    }

    // 公开方法，用于外部更新播放状态
    func updatePlayingStateFromPlayers() {
        updatePlayingState()
    }
    
    // MARK: - Mix Management
    func saveMix(name: String, description: String) -> MixPreset {
        let soundMixes = activeSounds.map { (soundId, player) in
            // 所有活跃的声音都应该在加载时启用，不管当前是否在播放
            SoundMix(soundId: soundId, volume: player.volume, isEnabled: true)
        }

        return MixPreset(name: name, description: description, soundMixes: soundMixes)
    }

    // 检查当前播放内容是否与原始收藏混音不同
    func hasCurrentMixBeenModified() -> Bool {
        guard let currentMix = currentPlayingMix else {
            return false
        }

        // 比较当前活跃声音与原始混音的声音
        let currentSoundIds = Set(activeSounds.keys)
        let originalSoundIds = Set(currentMix.soundMixes.map { $0.soundId })

        // 如果声音集合不同，则认为已修改
        if currentSoundIds != originalSoundIds {
            return true
        }

        // 如果声音集合相同，检查音量是否有变化
        for soundMix in currentMix.soundMixes {
            if let currentPlayer = activeSounds[soundMix.soundId] {
                // 允许小的浮点数误差
                let volumeDifference = abs(currentPlayer.volume - soundMix.volume)
                if volumeDifference > 0.01 {
                    return true
                }
            }
        }

        return false
    }
    
    func loadMix(_ preset: MixPreset, sounds: [SoundItem]) {
        stopAllSounds()

        // 设置当前播放的收藏混音
        currentPlayingMix = preset

        for mix in preset.soundMixes {
            if let sound = sounds.first(where: { $0.id == mix.soundId }) {
                var soundCopy = sound
                soundCopy.volume = mix.volume

                if mix.isEnabled {
                    // 临时清除 currentPlayingMix 以避免在 playSound 中被清除
                    let tempMix = currentPlayingMix
                    currentPlayingMix = nil

                    // 始终加载声音播放器（包括 VIP 声音）
                    _ = playSound(soundCopy)
                    setVolume(for: sound.id, volume: mix.volume)

                    // 如果是 VIP 声音且用户未订阅，则静音处理
                    if sound.isVIP && !SubscriptionManager.shared.isSubscribed {
                        if let player = activeSounds[sound.id] {
                            player.pause()  // 静音处理
                        }
                    }

                    // 恢复 currentPlayingMix
                    currentPlayingMix = tempMix
                }
            }
        }

    }



    // MARK: - Fade Effects
    func fadeIn(_ soundId: UUID, duration: TimeInterval = 2.0) {
        activeSounds[soundId]?.fadeIn(duration: duration)
    }
    
    func fadeOut(_ soundId: UUID, duration: TimeInterval = 2.0) {
        activeSounds[soundId]?.fadeOut(duration: duration)
    }
    
    func crossFade(from fromSoundId: UUID, to toSoundId: UUID, duration: TimeInterval = 3.0) {
        fadeOut(fromSoundId, duration: duration)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.fadeIn(toSoundId, duration: duration - 0.5)
        }
    }


}

// MARK: - SoundPlayerDelegate
extension AudioManager: SoundPlayerDelegate {
    func soundPlayerDidFinish(_ player: any SoundPlayerProtocol) {
        activeSounds.removeValue(forKey: player.soundId)
        updatePlayingState()
    }

    func soundPlayerDidFail(_ player: any SoundPlayerProtocol, error: Error) {
        activeSounds.removeValue(forKey: player.soundId)
        updatePlayingState()
    }
}

// MARK: - SoundPlayer Protocol
protocol SoundPlayerDelegate: AnyObject {
    func soundPlayerDidFinish(_ player: any SoundPlayerProtocol)
    func soundPlayerDidFail(_ player: any SoundPlayerProtocol, error: Error)
}

protocol SoundPlayerProtocol: ObservableObject {
    var soundId: UUID { get }
    var soundName: String { get }
    var isPlaying: Bool { get set }
    var volume: Float { get set }
    var delegate: SoundPlayerDelegate? { get set }

    func play()
    func pause()
    func stop()
    func setVolume(_ volume: Float)
    func fadeIn(duration: TimeInterval)
    func fadeOut(duration: TimeInterval)
}



// MARK: - Mock SoundPlayer Class (for when audio files are missing)
class MockSoundPlayer: ObservableObject, SoundPlayerProtocol {
    let soundId: UUID
    let soundName: String
    @Published var isPlaying: Bool = false
    @Published var volume: Float = 0.5
    weak var delegate: SoundPlayerDelegate?

    private var mockTimer: Timer?

    init(soundId: UUID, soundName: String, volume: Float = 0.5) {
        self.soundId = soundId
        self.soundName = soundName
        self.volume = volume
    }

    func play() {
        isPlaying = true
        // 模拟循环播放，不设置定时器自动停止
        // 只有手动调用 stop() 或音量为0时才停止
    }

    func pause() {
        isPlaying = false
        mockTimer?.invalidate()
    }

    func stop() {
        isPlaying = false
        mockTimer?.invalidate()
    }

    func setVolume(_ volume: Float) {
        self.volume = volume
        // 音量为0时不自动停止，只是静音播放
    }



    func fadeIn(duration: TimeInterval) {
        play()
    }

    func fadeOut(duration: TimeInterval) {
        DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
            self.stop()
        }
    }
}

// MARK: - SoundPlayer Class
class SoundPlayer: NSObject, ObservableObject, SoundPlayerProtocol {
    let soundId: UUID
    let soundName: String
    private var audioPlayer: AVAudioPlayer?
    private var fadeTimer: Timer?

    @Published var isPlaying: Bool = false
    @Published var volume: Float = 0.5

    weak var delegate: SoundPlayerDelegate?

    init(url: URL, soundId: UUID, soundName: String, volume: Float = 0.5) {
        self.soundId = soundId
        self.soundName = soundName
        self.volume = volume
        super.init()
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.numberOfLoops = -1 // Loop indefinitely
            audioPlayer?.volume = volume
            audioPlayer?.prepareToPlay()
        } catch {
        }
    }
    
    func play() {
        audioPlayer?.play()
        isPlaying = true
    }
    
    func pause() {
        audioPlayer?.pause()
        isPlaying = false
    }
    
    func stop() {
        audioPlayer?.stop()
        audioPlayer?.currentTime = 0
        isPlaying = false
        fadeTimer?.invalidate()
    }
    
    func setVolume(_ volume: Float) {
        self.volume = volume
        audioPlayer?.volume = volume
        // 音量为0时不自动停止，只是静音播放
    }
    

    
    // MARK: - Fade Effects
    func fadeIn(duration: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        player.volume = 0
        play()
        
        let steps = 20
        let stepDuration = duration / Double(steps)
        let volumeStep = volume / Float(steps)
        
        var currentStep = 0
        fadeTimer = Timer.scheduledTimer(withTimeInterval: stepDuration, repeats: true) { timer in
            currentStep += 1
            player.volume = volumeStep * Float(currentStep)
            
            if currentStep >= steps {
                timer.invalidate()
                player.volume = self.volume
            }
        }
    }
    
    func fadeOut(duration: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        let steps = 20
        let stepDuration = duration / Double(steps)
        let volumeStep = player.volume / Float(steps)
        
        var currentStep = 0
        fadeTimer = Timer.scheduledTimer(withTimeInterval: stepDuration, repeats: true) { timer in
            currentStep += 1
            player.volume = self.volume - (volumeStep * Float(currentStep))
            
            if currentStep >= steps {
                timer.invalidate()
                self.stop()
            }
        }
    }
}

// MARK: - AVAudioPlayerDelegate
extension SoundPlayer: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        // 对于循环播放，不应该调用 didFinish，因为音频应该一直循环
        // 只有在非循环模式下或者出错时才调用
        if player.numberOfLoops != -1 {
            isPlaying = false
            delegate?.soundPlayerDidFinish(self)
        }
    }

    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        isPlaying = false
        if let error = error {
            delegate?.soundPlayerDidFail(self, error: error)
        }
    }
}

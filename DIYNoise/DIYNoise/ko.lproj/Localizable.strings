/*
  Localizable.strings (Korean)
  DIYNoise
*/

// MARK: - App Name & Main
"app_name" = "DIY Noise";
"app_subtitle" = "나만의 화이트 노이즈 만들기";

// MARK: - Tab Bar
"tab_sounds" = "사운드";
"tab_favorites" = "즐겨찾기";
"tab_settings" = "설정";

// MARK: - Sound Categories
"category_rain" = "비 소리";
"category_rain_desc" = "다양한 비 소리로 평화로움을 느껴보세요";
"category_thunder" = "천둥 소리";
"category_thunder_desc" = "천둥 소리로 드라마틱한 분위기 연출";
"category_wind" = "바람 소리";
"category_wind_desc" = "산들바람부터 강풍까지 다양한 바람 소리";
"category_ocean" = "파도 소리";
"category_ocean_desc" = "파도가 부딪히는 편안한 소리";
"category_birds" = "새 소리";
"category_birds_desc" = "다양한 새들의 아름다운 노래";
"category_forest" = "숲 소리";
"category_forest_desc" = "숲속의 자연 소리";
"category_fire" = "불 소리";
"category_fire_desc" = "모닥불의 따뜻한 소리";
"category_city" = "도시 소리";
"category_city_desc" = "도시 환경의 배경 소리";
"category_insects" = "곤충 소리";
"category_insects_desc" = "자연 속 곤충들의 소리";

// MARK: - Sound Names
"rain_from_eaves" = "처마 비 소리";
"light_rain" = "가랑비 소리";
"heavy_rain" = "폭우 소리";
"distant_thunder" = "먼 곳의 천둥";
"thunder_rumbles" = "우르릉 천둥";
"high_altitude_thunder" = "고공 천둥";
"undulating_thunder" = "울림 천둥";
"blows_leaves" = "잎사귀 흔들리는 소리";
"strong_wind" = "강풍";
"winter_cold_wind" = "겨울 찬바람";
"desert_wind" = "사막 바람";
"breeze_rustles" = "바스락 바람";
"breeze_leaves" = "잎사귀 스치는 바람";
"water_wave" = "물결 소리";
"sea_wave" = "바다 파도";
"waves_on_shore" = "해변 파도";
"bubbling_underwater" = "수중 거품 소리";
"small_stream" = "작은 시냇물";
"morning_birds" = "아침 새 소리";
"forest_birds" = "숲속 새 소리";
"nightingale" = "나이팅게일";
"bird_chirping" = "새 지저귐";
"bird_outside" = "창밖 새 소리";
"distant_bird" = "먼 곳 새 소리";
"cuckoo" = "뻐꾸기";
"cricket_chirping" = "귀뚜라미 소리";
"midsummer_insect_chirping" = "한여름 곤충 소리";
"frog_sounds" = "개구리 소리";
"forest_insects" = "숲속 곤충 소리";
"summer_evening_frog" = "여름 저녁 개구리";
"cicadas_chirping" = "매미 울음 소리";
"bees_flying" = "벌 날개 소리";
"campfire" = "캠프파이어";
"fire_crackling" = "타는 불 소리";
"flame_sound" = "불꽃 소리";
"city_ambience" = "도시 배경음";
"coffee_shop" = "카페";
"flipping_books" = "책 넘기는 소리";
"office_ambience" = "사무실";
"typing_on_keyboard" = "키보드 타이핑";

"sound_rain_from_eaves" = "처마 비 소리";
"sound_rain_on_leaves" = "잎에 부딪히는 비";
"sound_light_rain" = "가랑비 소리";
"sound_heavy_rain" = "폭우 소리";
"sound_rain_with_thunder" = "비와 천둥";
"sound_distant_thunder" = "먼 곳의 천둥";
"sound_close_thunder" = "가까운 천둥";
"sound_thunderstorm" = "뇌우";
"sound_lightning_thunder" = "번개와 천둥";
"sound_gentle_breeze" = "산들바람";
"sound_strong_wind" = "강풍";
"sound_wind_through_trees" = "나무 사이 바람";
"sound_mountain_wind" = "산바람";
"sound_waves_on_shore" = "해변 파도";
"sound_deep_ocean_waves" = "깊은 바다 파도";
"sound_beach_waves" = "해변 파도 소리";
"sound_seagulls_and_waves" = "갈매기와 파도";
"sound_morning_birds" = "아침 새 소리";
"sound_forest_birds" = "숲속 새 소리";
"sound_nightingale" = "나이팅게일";
"sound_bird_chirping" = "새 지저귐";
"sound_forest_ambience" = "숲 배경음";
"sound_rustling_leaves" = "바스락 잎 소리";
"sound_forest_stream" = "숲속 시냇물";
"sound_forest_insects" = "숲속 곤충 소리";
"sound_campfire" = "캠프파이어";
"sound_fireplace" = "벽난로";
"sound_wood_burning" = "장작 타는 소리";
"sound_city_ambience" = "도시 배경음";
"sound_cafe" = "카페";
"sound_library" = "도서관";
"sound_office_ambience" = "사무실";
"sound_cricket_chirping" = "귀뚜라미 소리";
"sound_midsummer_insect_chirping" = "한여름 곤충 소리";
"sound_frog_sounds" = "개구리 소리";
"sound_thunder_rumbles" = "우르릉 천둥";
"sound_high_altitude_thunder" = "고공 천둥";
"sound_undulating_thunder" = "울림 천둥";
"sound_blows_leaves" = "잎사귀 흔들리는 소리";
"sound_winter_cold_wind" = "겨울 찬바람";
"sound_desert_wind" = "사막 바람";
"sound_breeze_rustles" = "바스락 바람";
"sound_breeze_leaves" = "잎사귀 스치는 바람";
"sound_water_wave" = "물결 소리";
"sound_sea_wave" = "바다 파도";
"sound_bubbling_underwater" = "수중 거품 소리";
"sound_small_stream" = "작은 시냇물";
"sound_bird_outside" = "창밖 새 소리";
"sound_distant_bird" = "먼 곳 새 소리";
"sound_cuckoo" = "뻐꾸기";
"sound_summer_evening_frog" = "여름 저녁 개구리";
"sound_cicadas_chirping" = "매미 울음 소리";
"sound_bees_flying" = "벌 날개 소리";
"sound_fire_crackling" = "타는 불 소리";
"sound_flame_sound" = "불꽃 소리";
"sound_coffee_shop" = "카페";
"sound_flipping_books" = "책 넘기는 소리";
"sound_typing_on_keyboard" = "키보드 타이핑";

"sound_traffic" = "교통 소음";
"sound_cafe" = "카페"; // (참고: 위와 중복, 동일 번역)
"sound_subway" = "지하철";
"sound_construction" = "공사장 소음";
"sound_office" = "사무실";
"sound_restaurant" = "레스토랑";
"sound_library" = "도서관"; // (참고: 위와 중복, 동일 번역)
"sound_airport" = "공항";

"sound_white_noise" = "화이트 노이즈";
"sound_pink_noise" = "핑크 노이즈";
"sound_brown_noise" = "브라운 노이즈";
"sound_blue_noise" = "블루 노이즈";
"sound_violet_noise" = "바이올렛 노이즈";
"sound_grey_noise" = "그레이 노이즈";

"sound_space" = "우주";
"sound_underwater" = "수중";
"sound_cave" = "동굴";
"sound_meditation" = "명상";
"sound_temple" = "사찰";
"sound_monastery" = "수도원";

// MARK: - Sound Descriptions
"rain_from_eaves_desc" = "처마에서 떨어지는 빗방울 소리";
"light_rain_desc" = "부드러운 가랑비 소리";
"heavy_rain_desc" = "세찬 폭우 소리";
"distant_thunder_desc" = "멀리서 들려오는 천둥 소리";
"thunder_rumbles_desc" = "깊게 울리는 천둥 소리";
"high_altitude_thunder_desc" = "높은 곳에서 들려오는 천둥 소리";
"undulating_thunder_desc" = "파동처럼 들리는 천둥 소리";
"blows_leaves_desc" = "바람에 흔들리는 잎사귀 소리";
"strong_wind_desc" = "강한 바람 소리";
"winter_cold_wind_desc" = "겨울에 부는 차가운 바람 소리";
"desert_wind_desc" = "건조한 사막 바람 소리";
"breeze_rustles_desc" = "가볍게 스치는 바람 소리";
"breeze_leaves_desc" = "잎사귀를 스치는 산들바람 소리";
"water_wave_desc" = "물결이 부드럽게 일렁이는 소리";
"sea_wave_desc" = "바다 파도가 출렁이는 소리";
"waves_on_shore_desc" = "파도가 해변에 부딪히는 소리";
"bubbling_underwater_desc" = "물속에서 거품이 일어나는 소리";
"small_stream_desc" = "작은 시냇물이 흐르는 소리";
"morning_birds_desc" = "아침에 들리는 새들의 합창";
"forest_birds_desc" = "숲속에서 들리는 새 소리";
"nightingale_desc" = "나이팅게일의 아름다운 노래";
"bird_chirping_desc" = "다양한 새들의 지저귐";
"bird_outside_desc" = "창밖에서 들리는 새 소리";
"distant_bird_desc" = "멀리서 들려오는 새 소리";
"cuckoo_desc" = "뻐꾸기 울음 소리";
"cricket_chirping_desc" = "밤에 들리는 귀뚜라미 소리";
"midsummer_insect_chirping_desc" = "한여름의 다양한 곤충 소리";
"frog_sounds_desc" = "평화로운 개구리 울음소리";
"forest_insects_desc" = "숲속의 곤충 소리";
"summer_evening_frog_desc" = "여름 저녁의 개구리 합창";
"cicadas_chirping_desc" = "여름 매미 울음 소리";
"bees_flying_desc" = "벌이 날아다니는 윙윙 소리";
"campfire_desc" = "모닥불이 타는 파지직 소리";
"fire_crackling_desc" = "불이 타는 파지직 소리";
"flame_sound_desc" = "안정적으로 타오르는 불꽃 소리";
"city_ambience_desc" = "도시의 배경 환경 소리";
"coffee_shop_desc" = "카페 환경 소리";
"flipping_books_desc" = "책장을 넘기는 소리";
"office_ambience_desc" = "사무실 배경 소리";
"typing_on_keyboard_desc" = "키보드 치는 소리";

"sound_rain_on_roof_desc" = "지붕에 부딪히는 빗방울 소리";
"sound_rain_on_leaves_desc" = "잎사귀에 떨어지는 빗방울 소리";
"sound_light_rain_desc" = "부드러운 가랑비 소리";
"sound_heavy_rain_desc" = "세찬 폭우 소리";
"sound_rain_with_thunder_desc" = "비 소리와 함께 들리는 먼 천둥";
"sound_distant_thunder_desc" = "멀리서 들려오는 천둥 소리";
"sound_close_thunder_desc" = "가까이서 들리는 천둥 소리";
"sound_thunderstorm_desc" = "천둥과 비 소리의 완벽한 조화";
"sound_lightning_thunder_desc" = "번개 뒤에 따르는 천둥 소리";
"sound_gentle_breeze_desc" = "부드러운 산들바람 소리";
"sound_strong_wind_desc" = "강한 바람 소리";
"sound_wind_through_trees_desc" = "나무 사이를 통과하는 바람 소리";
"sound_mountain_wind_desc" = "산에서 부는 바람 소리";
"sound_waves_on_shore_desc" = "파도가 해변에 부딪히는 소리";
"sound_deep_ocean_waves_desc" = "깊은 바다의 파도 소리";
"sound_beach_waves_desc" = "해변의 파도 소리";
"sound_seagulls_and_waves_desc" = "갈매기 울음소리와 파도 소리";
"sound_morning_birds_desc" = "아침에 들리는 새들의 합창";
"sound_forest_birds_desc" = "숲속에서 들리는 새 소리";
"sound_nightingale_desc" = "나이팅게일의 아름다운 노래";
"sound_bird_chirping_desc" = "다양한 새들의 지저귐";
"sound_forest_ambience_desc" = "숲의 전반적인 배경 소리";
"sound_rustling_leaves_desc" = "잎사귀 바스락거리는 소리";
"sound_forest_stream_desc" = "숲속 시냇물 흐르는 소리";
"sound_forest_insects_desc" = "숲속의 곤충 소리";
"sound_campfire_desc" = "모닥불이 타는 소리";
"sound_fireplace_desc" = "벽난로 불 소리";
"sound_wood_burning_desc" = "장작이 타는 소리";
"sound_city_ambience_desc" = "도시의 배경 환경 소리";
"sound_cafe_desc" = "카페 환경 소리";
"sound_library_desc" = "도서관의 조용한 환경 소리";
"sound_office_ambience_desc" = "사무실 배경 소리";
"sound_midsummer_insect_chirping_desc" = "한여름의 다양한 곤충 소리";
"sound_frog_sounds_desc" = "평화로운 개구리 울음소리";
"sound_thunder_rumbles_desc" = "깊게 울리는 천둥 소리";
"sound_high_altitude_thunder_desc" = "높은 곳에서 들려오는 천둥 소리";
"sound_undulating_thunder_desc" = "파동처럼 들리는 천둥 소리";
"sound_blows_leaves_desc" = "바람에 흔들리는 창밖 잎사귀";
"sound_winter_cold_wind_desc" = "겨울에 부는 차가운 바람 소리";
"sound_desert_wind_desc" = "건조한 사막 바람 소리";
"sound_breeze_rustles_desc" = "가볍게 스치는 바람 소리";
"sound_breeze_leaves_desc" = "잎사귀를 스치는 산들바람 소리";
"sound_water_wave_desc" = "물결이 부드럽게 일렁이는 소리";
"sound_sea_wave_desc" = "바다 파도가 출렁이는 소리";
"sound_bubbling_underwater_desc" = "물속에서 거품이 일어나는 소리";
"sound_small_stream_desc" = "작은 시냇물이 흐르는 소리";
"sound_bird_outside_desc" = "창밖에서 들리는 새 소리";
"sound_distant_bird_desc" = "멀리서 들려오는 새 소리";
"sound_cuckoo_desc" = "뻐꾸기 울음 소리";
"sound_summer_evening_frog_desc" = "여름 저녁의 개구리 합창";
"sound_cicadas_chirping_desc" = "여름 매미 울음 소리";
"sound_bees_flying_desc" = "벌이 날아다니는 윙윙 소리";
"sound_fire_crackling_desc" = "불이 타는 파지직 소리";
"sound_flame_sound_desc" = "안정적으로 타오르는 불꽃 소리";
"sound_coffee_shop_desc" = "카페 환경 소리";
"sound_flipping_books_desc" = "책장을 넘기는 소리";
"sound_typing_on_keyboard_desc" = "키보드 치는 소리";
"sound_rain_from_eaves_desc" = "처마에서 떨어지는 빗방울이 땅에 닿는 소리";
"sound_cricket_chirping_desc" = "조용한 귀뚜라미 소리";

"sound_traffic_desc" = "도시 교통 배경 소음";
"sound_cafe_desc" = "카페 환경 소리"; // (참고: 위와 중복, 동일 번역)
"sound_subway_desc" = "지하철 운행 소리";
"sound_construction_desc" = "건설 현장 소음";
"sound_office_desc" = "사무실 환경 소리";
"sound_restaurant_desc" = "레스토랑 배경 소리";
"sound_library_desc" = "도서관의 조용한 소리"; // (참고: 위와 중복, 동일 번역)
"sound_airport_desc" = "공항 환경 소리";

"sound_white_noise_desc" = "클래식한 화이트 노이즈";
"sound_pink_noise_desc" = "부드러운 핑크 노이즈";
"sound_brown_noise_desc" = "깊은 브라운 노이즈";
"sound_blue_noise_desc" = "맑은 블루 노이즈";
"sound_violet_noise_desc" = "고주파 바이올렛 노이즈";
"sound_grey_noise_desc" = "균형 잡힌 그레이 노이즈";

"sound_space_desc" = "우주의 신비로운 소리";
"sound_underwater_desc" = "수중의 고요한 소리";
"sound_cave_desc" = "동굴 메아리";
"sound_meditation_desc" = "명상 배경 소리";
"sound_temple_desc" = "사찰 종소리";
"sound_monastery_desc" = "수도원의 평온";

// MARK: - UI Elements
"sound_count" = "사운드 %d개";
"playing_count" = "%d/%d 재생 중";
"volume" = "볼륨";

// MARK: - Buttons
"play" = "재생";
"pause" = "일시정지";
"stop" = "중지";
"stop_all" = "모두 중지";
"save" = "저장";
"cancel" = "취소";
"delete" = "삭제";
"edit" = "편집";
"done" = "완료";
"close" = "닫기";
"back" = "뒤로";

// MARK: - Mixer
"mixer_title" = "믹서";
"active_sounds_count" = "활성 사운드 %d개";
"no_active_sounds" = "활성 사운드 없음";
"no_active_sounds_desc" = "메인 페이지로 돌아가 사운드를 선택하고 믹스를 시작하세요";
"save_mix" = "믹스 저장";
"save_as_new_mix" = "새 믹스로 저장";
"update_current_mix" = "저장";
"update_and_add_sounds" = "업데이트 및 사운드 추가";
"update_mix" = "믹스 업데이트";
"update_mix_prompt" = "현재 설정으로 이 믹스를 업데이트하시겠습니까?";
"saved_mixes" = "저장된 믹스";
"mix_name" = "믹스 이름";
"mix_description" = "설명";
"mix_name_placeholder" = "믹스 이름";
"mix_description_placeholder" = "설명 (선택사항)";
"mix_name_prompt" = "현재 믹스에 이름을 지정하세요";
"unnamed_mix" = "이름 없는 믹스";

// MARK: - Favorites
"favorites_title" = "내 즐겨찾기";
"favorites_subtitle" = "저장된 사운드 믹스";
"favorites_count" = "개 즐겨찾기";
"no_favorites" = "즐겨찾기 없음";
"no_favorites_desc" = "믹서에서 마음에 드는 사운드 조합을 저장하세요";
"delete_favorite" = "즐겨찾기 삭제";
"delete_favorite_message" = "이 즐겨찾기 믹스를 삭제하시겠습니까?";
"mix_sounds_count" = "사운드 %d개";
"mix_combination" = "믹스 조합";

// MARK: - Settings
"settings_title" = "설정";
"settings_subtitle" = "앱 맞춤 설정";
"appearance_section" = "디자인";
"language_section" = "언어";
"playback_section" = "재생";
"about_section" = "정보";

// 디자인 설정
"theme_color" = "테마 색상";
"dark_mode" = "다크 모드";
"follow_system" = "시스템 설정 따르기";

// 언어 설정
"app_language" = "앱 언어";
"system_language_settings" = "시스템 설정으로 이동";

// 재생 설정
"auto_play" = "자동 재생";
"auto_play_desc" = "앱 열 때 마지막 재생 믹스 자동 복원";
"background_play" = "백그라운드 재생";
"background_play_desc" = "다른 앱으로 전환해도 사운드 계속 재생";
"lock_screen_control" = "잠금 화면 제어";
"lock_screen_control_desc" = "앱을 열지 않고 잠금 화면 및 제어 센터에서 바로 재생 제어";

// Lock Screen Control VIP
"lock_screen_control_title" = "잠금 화면 제어";
"lock_screen_control_vip_desc" = "VIP로 업그레이드하여 잠금 화면 및 제어 센터에서 재생 제어";
"lock_screen_control_feature" = "잠금 화면 제어";
"lock_screen_control_feature_desc" = "잠금 화면 및 제어 센터에서 재생 제어";
"lock_screen_control_benefit1" = "기기 잠금 해제 없이 재생 제어";
"lock_screen_control_benefit2" = "제어 센터에서 빠르게 접근";
"lock_screen_control_benefit3" = "iOS 미디어 제어와 완벽 통합";

// Auto Play Messages
"auto_play_enabled" = "자동 재생 켜짐";
"auto_play_disabled" = "자동 재생 꺼짐";
"no_last_mix_found" = "마지막 재생 믹스를 찾을 수 없음";
"last_mix_loaded" = "마지막 재생 믹스 불러옴";
"auto_play_mix_name" = "자동 재생 믹스";
"auto_play_mix_desc" = "자동 재생 기능을 위해 저장된 믹스";

// Background Play Messages
"background_play_enabled" = "백그라운드 재생 켜짐";
"background_play_disabled" = "백그라운드 재생 꺼짐";

// 정보
"version" = "버전";
"feedback" = "피드백";
"feedback_desc" = "피드백 및 제안 보내기";
"rate_app" = "평가하기";
"rate_app_desc" = "App Store에서 평가하기";

// MARK: - Timer
"timer" = "타이머";
"timer_remaining" = "남은 시간 %@";
"set_timer" = "타이머 설정";
"timer_minutes" = "분";
"timer_hours" = "시간";

// MARK: - Alerts & Messages
"error" = "오류";
"success" = "성공";
"loading" = "로딩 중...";
"saved_successfully" = "성공적으로 저장됨";
"deleted_successfully" = "성공적으로 삭제됨";

// MARK: - Accessibility
"play_button" = "재생 버튼";
"pause_button" = "일시정지 버튼";
"volume_slider" = "볼륨 슬라이더";
"close_button" = "닫기 버튼";

// MARK: - VIP Subscription
// VIP Feature Titles
"vip_sound_title" = "VIP 전용 사운드";
"mix_limit_title" = "무제한 믹싱";
"save_limit_title" = "무제한 저장";
"vip_theme_title" = "VIP 전용 테마";
"long_timer_title" = "긴 타이머";

// VIP Feature Descriptions
"vip_sound_desc" = "이 사운드는 VIP 멤버십이 필요합니다. VIP로 업그레이드하여 모든 고품질 전용 사운드를 잠금 해제하세요!";
"mix_limit_desc" = "무료 사용자는 최대 %d개의 사운드만 동시 재생 가능합니다. VIP로 업그레이드하여 무제한 믹싱 경험을 누리세요!";
"save_limit_desc" = "무료 사용자는 최대 %d개의 즐겨찾기만 저장 가능합니다. VIP로 업그레이드하여 무제한 저장을 즐기세요!";
"vip_theme_desc" = "이 테마는 VIP 멤버십이 필요합니다. VIP로 업그레이드하여 모든 아름다운 테마를 잠금 해제하세요!";
"long_timer_desc" = "무료 사용자는 최대 30분까지만 타이머 설정 가능합니다. VIP로 업그레이드하여 더 긴 타이머 재생을 즐기세요!";

// VIP Benefits
"upgrade_will_get" = "업그레이드 시 다음을 얻습니다:";
"all_vip_benefits" = "VIP 멤버십의 모든 혜택";

// VIP Sound Benefits
"vip_sound_benefit_1" = "모든 VIP 전용 사운드 잠금 해제";
"vip_sound_benefit_2" = "고품질 오디오 경험";
"vip_sound_benefit_3" = "독점 사운드 콘텐츠";

// Mix Limit Benefits
"mix_limit_benefit_1" = "믹스에 무제한 사운드 추가";
"mix_limit_benefit_2" = "복잡한 사운드 조합 생성";
"mix_limit_benefit_3" = "프로 수준의 믹싱 경험";

// Save Limit Benefits
"save_limit_benefit_1" = "무제한 즐겨찾기 저장";
"save_limit_benefit_2" = "개인 사운드 라이브러리 생성";
"save_limit_benefit_3" = "즐겨찾는 믹스에 언제든 접근";

// VIP Theme Benefits
"vip_theme_benefit_1" = "모든 아름다운 테마 잠금 해제";
"vip_theme_benefit_2" = "개인화된 인터페이스 경험";
"vip_theme_benefit_3" = "독점 비주얼 디자인";

// Long Timer Benefits
"long_timer_benefit_1" = "30분 이상 타이머 설정";
"long_timer_benefit_2" = "휴식 시간 연장";
"long_timer_benefit_3" = "수면 및 명상에 완벽";

// VIP Feature List (for all features view)
"vip_sound_feature" = "VIP 전용 사운드";
"vip_sound_feature_desc" = "고품질 전용 사운드 잠금 해제";
"mix_limit_feature" = "무제한 믹싱";
"mix_limit_feature_desc" = "믹스에 무제한 사운드 추가";
"save_limit_feature" = "무제한 저장";
"save_limit_feature_desc" = "무제한 즐겨찾기 저장";
"vip_theme_feature" = "VIP 전용 테마";
"vip_theme_feature_desc" = "모든 아름다운 테마 사용";
"long_timer_feature" = "긴 타이머";
"long_timer_feature_desc" = "30분 이상 타이머 설정";
"lock_screen_control_feature" = "잠금 화면 제어"; // 설정 기반 추가
"lock_screen_control_feature_desc" = "잠금 화면에서 직접 재생 제어"; // 설정 기반 추가

// Action Buttons
"upgrade_to_vip" = "VIP로 업그레이드";
"later_button" = "나중에";
"tap_to_upgrade" = "업그레이드하기";

// MARK: - Theme Names
"theme_ocean_breeze" = "바닷바람";
"theme_sunset_glow" = "석양";
"theme_forest_mist" = "숲 안개";
"theme_night_sky" = "밤하늘";
"theme_lavender_dream" = "라벤더 꿈";
"theme_fire_ember" = "불꽃 잔불";

// MARK: - Theme Selector
"select_theme" = "선택하여 사용";
"current_theme" = "현재: %@";
"theme_in_use" = "사용 중";
"previous_theme" = "이전";
"next_theme" = "다음";

// 테마 변경 확인
"confirm_theme_change" = "테마 변경 확인";
"confirm_theme_change_message" = "\"%@\" 테마로 전환하시겠습니까?";
"confirm" = "확인";

// 즐겨찾기 삭제 확인
"delete_favorite_confirm" = "즐겨찾기 믹스 삭제";
"delete_favorite_message" = "마지막 사운드를 삭제하면 즐겨찾기 믹스 \"%@\"도 삭제됩니다. 계속하시겠습니까?";
"delete_favorite" = "즐겨찾기 삭제";

// MARK: - Timer
"timer" = "타이머";
"time_remaining" = "남은 시간";
"set_timer" = "타이머 설정";
"timer_description" = "재생 시간을 선택하면, 시간이 되면 자동으로 중지됩니다";
"quick_select" = "빠른 선택";
"custom_time" = "사용자 지정 시간";
"hours" = "시간";
"minutes" = "분";
"seconds" = "초";
"start_timer" = "타이머 시작";
"cancel_timer" = "타이머 취소";
"elapsed_time" = "%@ 재생 중";

// Timer durations
"timer_15min" = "15분";
"timer_30min" = "30분";
"timer_45min" = "45분";
"timer_1hour" = "1시간";
"timer_1_5hour" = "1.5시간";
"timer_2hour" = "2시간";

// Now Playing Info
"playing_count" = "%d / %d개 사운드 재생 중";
"active_sounds_count" = "활성 사운드 %d개";
"app_subtitle" = "환경음 믹서";

// MARK: - Subscription Plans
"monthly_subscription" = "월간 구독";
"yearly_subscription" = "연간 구독";
"lifetime_purchase" = "평생 구매";
"popular_badge" = "인기";
"best_value_badge" = "가장 합리적";
"one_time_purchase" = "일회성 구매";
"cancel_anytime" = "언제든지 취소 가능";
"save_over_30_percent" = "월간 구독 대비 30% 이상 절약";
"lifetime_description" = "한 번 구매로 모든 VIP 기능 평생 이용";
"choose_subscription_plan" = "구독 플랜 선택";
"loading_subscription_options" = "구독 옵션 로딩 중...";
"restore_purchases" = "구매 복원";
"terms_of_service" = "이용약관";
"privacy_policy" = "개인정보처리방침";
"subscription_success" = "구독 성공! 지원해 주셔서 감사합니다!";
"purchase_failed" = "구매 실패: %@";
"restore_success" = "구매 복원 성공!";
"no_purchases_to_restore" = "복원할 구매 내역 없음";
"processing" = "처리 중...";

// MARK: - Subscription Interface
"upgrade_to_vip_title" = "VIP로 업그레이드";
"upgrade_subtitle" = "모든 프리미엄 기능 잠금 해제 및 완전한 DIY Noise 경험";
"vip_membership_benefits" = "VIP 멤버십 혜택";
"preview_mode_notice" = "미리보기 모드 - App Store Connect 제품 구성 필요";

// Subscription Period
"daily" = "일";
"weekly" = "주";
"monthly" = "월";
"yearly" = "년";

// VIP Features (구독 화면용 재정의)
"vip_exclusive_sounds" = "VIP 전용 사운드";
"vip_exclusive_sounds_desc" = "모든 고품질 전용 사운드 잠금 해제";
"unlimited_mixing" = "무제한 믹싱";
"unlimited_mixing_desc" = "믹스에 무제한 사운드 추가";
"unlimited_favorites" = "무제한 즐겨찾기";
"unlimited_favorites_desc" = "무제한 즐겨찾기 믹스 저장";
"vip_exclusive_themes" = "VIP 전용 테마";
"vip_exclusive_themes_desc" = "모든 아름다운 인터페이스 테마 사용";
"lock_screen_control_feature" = "잠금 화면 제어";
"lock_screen_control_feature_desc" = "잠금 화면 및 제어 센터에서 직접 재생 제어";
"extended_timer" = "긴 타이머";
"extended_timer_desc" = "30분 이상 타이머 재생 설정";

// Alert
"alert_title" = "알림";
"ok_button" = "확인";

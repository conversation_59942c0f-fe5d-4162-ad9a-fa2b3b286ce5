import Foundation

// MARK: - Localization Helper
struct LocalizedString {
    static func localized(_ key: String, comment: String = "") -> String {
        return NSLocalizedString(key, comment: comment)
    }
    
    static func localized(_ key: String, arguments: CVarArg...) -> String {
        let format = NSLocalizedString(key, comment: "")
        return String(format: format, arguments: arguments)
    }
}

// MARK: - Convenience Extensions
extension String {
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    func localized(with arguments: CVarArg...) -> String {
        return String(format: self.localized, arguments: arguments)
    }
}

// MARK: - Localized Strings Constants
struct L10n {
    // MARK: - App
    static let appName = "app_name".localized
    static let appSubtitle = "app_subtitle".localized
    
    // MARK: - Tab Bar
    static let tabSounds = "tab_sounds".localized
    static let tabFavorites = "tab_favorites".localized
    static let tabSettings = "tab_settings".localized
    
    // MARK: - Categories
    static let categoryNature = "category_nature".localized
    static let categoryNatureDesc = "category_nature_desc".localized
    static let categoryUrban = "category_urban".localized
    static let categoryUrbanDesc = "category_urban_desc".localized
    static let categoryWhiteNoise = "category_white_noise".localized
    static let categoryWhiteNoiseDesc = "category_white_noise_desc".localized
    static let categoryAmbient = "category_ambient".localized
    static let categoryAmbientDesc = "category_ambient_desc".localized
    
    // MARK: - UI Elements
    static func soundCount(_ count: Int) -> String {
        return "sound_count".localized(with: count)
    }
    
    static func playingCount(_ playing: Int, _ total: Int) -> String {
        return "playing_count".localized(with: playing, total)
    }
    
    static let volume = "volume".localized
    
    // MARK: - Buttons
    static let play = "play".localized
    static let pause = "pause".localized
    static let stop = "stop".localized
    static let stopAll = "stop_all".localized
    static let save = "save".localized
    static let cancel = "cancel".localized
    static let delete = "delete".localized
    static let edit = "edit".localized
    static let done = "done".localized
    static let close = "close".localized
    static let back = "back".localized
    
    // MARK: - Mixer
    static let mixerTitle = "mixer_title".localized
    static func activeSoundsCount(_ count: Int) -> String {
        return "active_sounds_count".localized(with: count)
    }
    static let noActiveSounds = "no_active_sounds".localized
    static let noActiveSoundsDesc = "no_active_sounds_desc".localized
    static let saveMix = "save_mix".localized
    static let saveAsNewMix = "save_as_new_mix".localized
    static let updateCurrentMix = "update_current_mix".localized
    static let updateAndAddSounds = "update_and_add_sounds".localized
    static let updateMix = "update_mix".localized
    static let updateMixPrompt = "update_mix_prompt".localized
    static let savedMixes = "saved_mixes".localized
    static let mixName = "mix_name".localized
    static let mixDescription = "mix_description".localized
    static let mixNamePlaceholder = "mix_name_placeholder".localized
    static let mixDescriptionPlaceholder = "mix_description_placeholder".localized
    static let mixNamePrompt = "mix_name_prompt".localized
    static let unnamedMix = "unnamed_mix".localized
    
    // MARK: - Favorites
    static let favoritesTitle = "favorites_title".localized
    static let favoritesSubtitle = "favorites_subtitle".localized
    static let favoritesCount = "favorites_count".localized
    static let noFavorites = "no_favorites".localized
    static let noFavoritesDesc = "no_favorites_desc".localized
    static let deleteFavorite = "delete_favorite".localized
    static let deleteFavoriteMessage = "delete_favorite_message".localized
    static func mixSoundsCount(_ count: Int) -> String {
        return "mix_sounds_count".localized(with: count)
    }
    static let mixCombination = "mix_combination".localized
    
    // MARK: - Settings
    static let settingsTitle = "settings_title".localized
    static let settingsSubtitle = "settings_subtitle".localized
    static let appearanceSection = "appearance_section".localized
    static let languageSection = "language_section".localized
    static let playbackSection = "playback_section".localized
    static let aboutSection = "about_section".localized

    // 外观设置
    static let themeColor = "theme_color".localized
    static let darkMode = "dark_mode".localized
    static let followSystem = "follow_system".localized

    // 语言设置
    static let appLanguage = "app_language".localized
    static let systemLanguageSettings = "system_language_settings".localized

    // 播放设置
    static let autoPlay = "auto_play".localized
    static let autoPlayDesc = "auto_play_desc".localized
    static let backgroundPlay = "background_play".localized
    static let backgroundPlayDesc = "background_play_desc".localized

    // 关于
    static let version = "version".localized
    static let feedback = "feedback".localized
    static let feedbackDesc = "feedback_desc".localized
    static let rateApp = "rate_app".localized
    static let rateAppDesc = "rate_app_desc".localized
    
    // MARK: - Timer
    static let timer = "timer".localized
    static func timerRemaining(_ time: String) -> String {
        return "timer_remaining".localized(with: time)
    }
    static let setTimer = "set_timer".localized
    static let timerMinutes = "timer_minutes".localized
    static let timerHours = "timer_hours".localized
    
    // MARK: - Alerts
    static let error = "error".localized
    static let success = "success".localized
    static let loading = "loading".localized
    static let savedSuccessfully = "saved_successfully".localized
    static let deletedSuccessfully = "deleted_successfully".localized
    
    // MARK: - Accessibility
    static let playButton = "play_button".localized
    static let pauseButton = "pause_button".localized
    static let volumeSlider = "volume_slider".localized
    static let closeButton = "close_button".localized
}

// MARK: - Sound Localization Helper
struct SoundLocalizer {
    static func localizedName(for soundKey: String) -> String {
        return "sound_\(soundKey)".localizedWithManager
    }

    static func localizedDescription(for soundKey: String) -> String {
        return "sound_\(soundKey)_desc".localizedWithManager
    }

    static func localizedCategoryName(for categoryKey: String) -> String {
        return "category_\(categoryKey)".localizedWithManager
    }

    static func localizedCategoryDescription(for categoryKey: String) -> String {
        return "category_\(categoryKey)_desc".localizedWithManager
    }
}

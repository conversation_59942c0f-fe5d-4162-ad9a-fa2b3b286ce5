/* 
  Localizable.strings (English)
  DIYNoise
*/

// MARK: - App Name & Main
"app_name" = "DIY Noise";
"app_subtitle" = "Create Your Personal White Noise";

// MARK: - Tab Bar
"tab_sounds" = "Sounds";
"tab_favorites" = "Favorites";
"tab_settings" = "Settings";

// MARK: - Sound Categories
"category_rain" = "Rain";
"category_rain_desc" = "Various rain sounds bringing tranquility";
"category_thunder" = "Thunder";
"category_thunder_desc" = "Thunder sounds creating dramatic atmosphere";
"category_wind" = "Wind";
"category_wind_desc" = "From gentle breeze to strong wind sounds";
"category_ocean" = "Ocean";
"category_ocean_desc" = "Soothing sounds of waves";
"category_birds" = "Birds";
"category_birds_desc" = "Beautiful songs of various birds";
"category_forest" = "Forest";
"category_forest_desc" = "Natural sounds from the forest";
"category_fire" = "Fire";
"category_fire_desc" = "Warm sounds of burning fire";
"category_city" = "City";
"category_city_desc" = "Urban environment background sounds";
"category_insects" = "Insects";
"category_insects_desc" = "The sounds of insects in nature";


// MARK: - Sound Names
"rain_from_eaves" = "Rain from Eaves";
"light_rain" = "Light Rain";
"heavy_rain" = "Heavy Rain";
"distant_thunder" = "Distant Thunder";
"thunder_rumbles" = "Thunder Rumbles";
"high_altitude_thunder" = "High-altitude Thunder";
"undulating_thunder" = "Undulating Thunder";
"blows_leaves" = "Wind Blows Leaves";
"strong_wind" = "Strong Wind";
"winter_cold_wind" = "Winter Cold Wind";
"desert_wind" = "Desert Wind";
"breeze_rustles" = "Breeze Rustles";
"breeze_leaves" = "Breeze Leaves";
"water_wave" = "Water Wave";
"sea_wave" = "Sea Wave";
"waves_on_shore" = "Waves on Shore";
"bubbling_underwater" = "Bubbling Underwater";
"small_stream" = "Small Stream";
"morning_birds" = "Morning Birds";
"forest_birds" = "Forest Birds";
"nightingale" = "Nightingale";
"bird_chirping" = "Bird Chirping";
"bird_outside" = "Bird Outside";
"distant_bird" = "Distant Bird";
"cuckoo" = "Cuckoo";
"cricket_chirping" = "Cricket Chirping";
"midsummer_insect_chirping" = "Midsummer Insect Chirping";
"frog_sounds" = "Frog Sounds";
"forest_insects" = "Forest Insects";
"summer_evening_frog" = "Summer Evening Frog";
"cicadas_chirping" = "Cicadas Chirping";
"bees_flying" = "Bees Flying";
"campfire" = "Campfire";
"fire_crackling" = "Fire Crackling";
"flame_sound" = "Flame Sound";
"city_ambience" = "City Ambience";
"coffee_shop" = "Coffee Shop";
"flipping_books" = "Flipping Books";
"office_ambience" = "Office Ambience";
"typing_on_keyboard" = "Typing on Keyboard";

"sound_rain_from_eaves" = "Rain from Eaves";
"sound_rain_on_leaves" = "Rain on Leaves";
"sound_light_rain" = "Light Rain";
"sound_heavy_rain" = "Heavy Rain";
"sound_rain_with_thunder" = "Rain with Thunder";
"sound_distant_thunder" = "Distant Thunder";
"sound_close_thunder" = "Close Thunder";
"sound_thunderstorm" = "Thunderstorm";
"sound_lightning_thunder" = "Lightning Thunder";
"sound_gentle_breeze" = "Gentle Breeze";
"sound_strong_wind" = "Strong Wind";
"sound_wind_through_trees" = "Wind Through Trees";
"sound_mountain_wind" = "Mountain Wind";
"sound_waves_on_shore" = "Waves on Shore";
"sound_deep_ocean_waves" = "Deep Ocean Waves";
"sound_beach_waves" = "Beach Waves";
"sound_seagulls_and_waves" = "Seagulls and Waves";
"sound_morning_birds" = "Morning Birds";
"sound_forest_birds" = "Forest Birds";
"sound_nightingale" = "Nightingale";
"sound_bird_chirping" = "Bird Chirping";
"sound_forest_ambience" = "Forest Ambience";
"sound_rustling_leaves" = "Rustling Leaves";
"sound_forest_stream" = "Forest Stream";
"sound_forest_insects" = "Forest Insects";
"sound_campfire" = "Campfire";
"sound_fireplace" = "Fireplace";
"sound_wood_burning" = "Wood Burning";
"sound_city_ambience" = "City Ambience";
"sound_cafe" = "Cafe";
"sound_library" = "Library";
"sound_office_ambience" = "Office Ambience";
"sound_cricket_chirping" = "Cricket Chirping";
"sound_midsummer_insect_chirping" = "Midsummer Insect Chirping";
"sound_frog_sounds" = "Frog Sounds";
"sound_thunder_rumbles" = "Thunder Rumbles";
"sound_high_altitude_thunder" = "High-altitude Thunder";
"sound_undulating_thunder" = "Undulating Thunder";
"sound_blows_leaves" = "Wind Blows Leaves";
"sound_winter_cold_wind" = "Winter Cold Wind";
"sound_desert_wind" = "Desert Wind";
"sound_breeze_rustles" = "Breeze Rustles";
"sound_breeze_leaves" = "Breeze Leaves";
"sound_water_wave" = "Water Wave";
"sound_sea_wave" = "Sea Wave";
"sound_bubbling_underwater" = "Bubbling Underwater";
"sound_small_stream" = "Small Stream";
"sound_bird_outside" = "Bird Outside";
"sound_distant_bird" = "Distant Bird";
"sound_cuckoo" = "Cuckoo";
"sound_summer_evening_frog" = "Summer Evening Frog";
"sound_cicadas_chirping" = "Cicadas Chirping";
"sound_bees_flying" = "Bees Flying";
"sound_fire_crackling" = "Fire Crackling";
"sound_flame_sound" = "Flame Sound";
"sound_coffee_shop" = "Coffee Shop";
"sound_flipping_books" = "Flipping Books";
"sound_typing_on_keyboard" = "Typing on Keyboard";

"sound_traffic" = "Traffic";
"sound_cafe" = "Cafe";
"sound_subway" = "Subway";
"sound_construction" = "Construction";
"sound_office" = "Office";
"sound_restaurant" = "Restaurant";
"sound_library" = "Library";
"sound_airport" = "Airport";

"sound_white_noise" = "White Noise";
"sound_pink_noise" = "Pink Noise";
"sound_brown_noise" = "Brown Noise";
"sound_blue_noise" = "Blue Noise";
"sound_violet_noise" = "Violet Noise";
"sound_grey_noise" = "Grey Noise";

"sound_space" = "Space";
"sound_underwater" = "Underwater";
"sound_cave" = "Cave";
"sound_meditation" = "Meditation";
"sound_temple" = "Temple";
"sound_monastery" = "Monastery";

// MARK: - Sound Descriptions
"rain_from_eaves_desc" = "Rain dripping from the eaves";
"light_rain_desc" = "Gentle light rain falling";
"heavy_rain_desc" = "Intense heavy rain pouring";
"distant_thunder_desc" = "Thunder rumbling in the distance";
"thunder_rumbles_desc" = "Deep thunder rumbling sounds";
"high_altitude_thunder_desc" = "Thunder echoing from high altitude";
"undulating_thunder_desc" = "Rolling waves of thunder";
"blows_leaves_desc" = "Wind blowing through leaves";
"strong_wind_desc" = "Powerful wind gusting";
"winter_cold_wind_desc" = "Cold winter wind howling";
"desert_wind_desc" = "Dry desert wind blowing";
"breeze_rustles_desc" = "Gentle breeze rustling";
"breeze_leaves_desc" = "Soft breeze moving leaves";
"water_wave_desc" = "Gentle water waves lapping";
"sea_wave_desc" = "Ocean waves rolling";
"waves_on_shore_desc" = "Waves washing onto shore";
"bubbling_underwater_desc" = "Underwater bubbling sounds";
"small_stream_desc" = "Small stream flowing gently";
"morning_birds_desc" = "Morning bird chorus";
"forest_birds_desc" = "Forest bird songs";
"nightingale_desc" = "Beautiful nightingale song";
"bird_chirping_desc" = "Various birds chirping";
"bird_outside_desc" = "Birds singing outside";
"distant_bird_desc" = "Distant bird calls";
"cuckoo_desc" = "Cuckoo bird calling";
"cricket_chirping_desc" = "Crickets chirping at night";
"midsummer_insect_chirping_desc" = "Mixed insect sounds in midsummer";
"frog_sounds_desc" = "Peaceful frog sounds";
"forest_insects_desc" = "Forest insect chorus";
"summer_evening_frog_desc" = "Summer evening frog chorus";
"cicadas_chirping_desc" = "Cicadas chirping in summer";
"bees_flying_desc" = "Bees buzzing and flying";
"campfire_desc" = "Campfire crackling and burning";
"fire_crackling_desc" = "Fire crackling and popping";
"flame_sound_desc" = "Steady flame burning";
"city_ambience_desc" = "Urban city atmosphere";
"coffee_shop_desc" = "Coffee shop ambiance";
"flipping_books_desc" = "Pages turning and flipping";
"office_ambience_desc" = "Office background sounds";
"typing_on_keyboard_desc" = "Keyboard typing sounds";

"sound_rain_on_roof_desc" = "Raindrops hitting the roof";
"sound_rain_on_leaves_desc" = "Rain falling on leaves";
"sound_light_rain_desc" = "Gentle light rain";
"sound_heavy_rain_desc" = "Intense heavy rain";
"sound_rain_with_thunder_desc" = "Rain with distant thunder";
"sound_distant_thunder_desc" = "Thunder from afar";
"sound_close_thunder_desc" = "Close thunder sounds";
"sound_thunderstorm_desc" = "Perfect blend of thunder and rain";
"sound_lightning_thunder_desc" = "Thunder after lightning";
"sound_gentle_breeze_desc" = "Soft gentle breeze";
"sound_strong_wind_desc" = "Strong wind sounds";
"sound_wind_through_trees_desc" = "Wind blowing through trees";
"sound_mountain_wind_desc" = "Mountain wind sounds";
"sound_waves_on_shore_desc" = "Waves hitting the shore";
"sound_deep_ocean_waves_desc" = "Deep ocean wave sounds";
"sound_beach_waves_desc" = "Beach wave sounds";
"sound_seagulls_and_waves_desc" = "Seagulls with ocean waves";
"sound_morning_birds_desc" = "Morning bird chorus";
"sound_forest_birds_desc" = "Forest bird songs";
"sound_nightingale_desc" = "Beautiful nightingale song";
"sound_bird_chirping_desc" = "Various bird chirping";
"sound_forest_ambience_desc" = "Overall forest atmosphere";
"sound_rustling_leaves_desc" = "Leaves rustling sounds";
"sound_forest_stream_desc" = "Forest stream flowing";
"sound_forest_insects_desc" = "Forest insect sounds";
"sound_campfire_desc" = "Campfire burning sounds";
"sound_fireplace_desc" = "Fireplace flame sounds";
"sound_wood_burning_desc" = "Wood burning sounds";
"sound_city_ambience_desc" = "City background atmosphere";
"sound_cafe_desc" = "Coffee shop ambiance";
"sound_library_desc" = "Quiet library atmosphere";
"sound_office_ambience_desc" = "Office background sounds";
"sound_midsummer_insect_chirping_desc" = "Mixed insect sounds in midsummer";
"sound_frog_sounds_desc" = "Peaceful frog sounds";
"sound_thunder_rumbles_desc" = "Deep thunder rumbling sounds";
"sound_high_altitude_thunder_desc" = "Thunder echoing from high altitude";
"sound_undulating_thunder_desc" = "Rolling waves of thunder";
"sound_blows_leaves_desc" = "The wind blows the leaves outside the window";
"sound_winter_cold_wind_desc" = "Cold winter wind howling";
"sound_desert_wind_desc" = "Dry desert wind blowing";
"sound_breeze_rustles_desc" = "Gentle breeze rustling through";
"sound_breeze_leaves_desc" = "Soft breeze moving leaves";
"sound_water_wave_desc" = "Gentle water waves lapping";
"sound_sea_wave_desc" = "Ocean waves rolling";
"sound_bubbling_underwater_desc" = "Underwater bubbling sounds";
"sound_small_stream_desc" = "Small stream flowing gently";
"sound_bird_outside_desc" = "Birds singing outside";
"sound_distant_bird_desc" = "Distant bird calls";
"sound_cuckoo_desc" = "Cuckoo bird calling";
"sound_summer_evening_frog_desc" = "Summer evening frog chorus";
"sound_cicadas_chirping_desc" = "Cicadas chirping in summer";
"sound_bees_flying_desc" = "Bees buzzing and flying";
"sound_fire_crackling_desc" = "Fire crackling and popping";
"sound_flame_sound_desc" = "Steady flame burning";
"sound_coffee_shop_desc" = "Coffee shop ambiance";
"sound_flipping_books_desc" = "Pages turning and flipping";
"sound_typing_on_keyboard_desc" = "Keyboard typing sounds";
"sound_cricket_chirping_desc" = "crickets chirping at night";
"sound_rain_from_eaves_desc" = "Rain dripping from the eaves";

"sound_white_noise_desc" = "Classic white noise";
"sound_pink_noise_desc" = "Gentle pink noise";
"sound_brown_noise_desc" = "Deep brown noise";
"sound_blue_noise_desc" = "Crisp blue noise";
"sound_violet_noise_desc" = "High-frequency violet noise";
"sound_grey_noise_desc" = "Balanced grey noise";

"sound_space_desc" = "Mysterious space sounds";
"sound_underwater_desc" = "Peaceful underwater sounds";
"sound_cave_desc" = "Cave echoes";
"sound_meditation_desc" = "Meditation background";
"sound_temple_desc" = "Temple bells";
"sound_monastery_desc" = "Monastery tranquility";

// MARK: - UI Elements
"sound_count" = "%d sounds";
"playing_count" = "%d/%d playing";
"volume" = "Volume";

// MARK: - Buttons
"play" = "Play";
"pause" = "Pause";
"stop" = "Stop";
"stop_all" = "Stop All";
"save" = "Save";
"cancel" = "Cancel";
"delete" = "Delete";
"edit" = "Edit";
"done" = "Done";
"close" = "Close";
"back" = "Back";

// MARK: - Mixer
"mixer_title" = "Mixer";
"active_sounds_count" = "%d active sounds";
"no_active_sounds" = "No Active Sounds";
"no_active_sounds_desc" = "Return to main page to select sounds and start mixing";
"save_mix" = "Save Mix";
"save_as_new_mix" = "Save as New";
"update_current_mix" = "Save";
"update_and_add_sounds" = "Update & Add Sounds";
"update_mix" = "Update Mix";
"update_mix_prompt" = "Are you sure you want to update this mix with current settings?";
"saved_mixes" = "Saved Mixes";
"mix_name" = "Mix Name";
"mix_description" = "Description";
"mix_name_placeholder" = "Mix name";
"mix_description_placeholder" = "Description (optional)";
"mix_name_prompt" = "Set a name for the current mix";
"unnamed_mix" = "Unnamed Mix";

// MARK: - Favorites
"favorites_title" = "My Favorites";
"favorites_subtitle" = "Saved mix combinations";
"favorites_count" = "favorites";
"no_favorites" = "No Favorites";
"no_favorites_desc" = "Save your favorite sound combinations in the mixer";
"delete_favorite" = "Delete Favorite";
"delete_favorite_message" = "Are you sure you want to delete this favorite mix?";
"mix_sounds_count" = "%d sounds";
"mix_combination" = "Mix Combination";

// MARK: - Settings
"settings_title" = "Settings";
"settings_subtitle" = "Personalize your app";
"appearance_section" = "System";
"language_section" = "Language";
"playback_section" = "Playback";
"about_section" = "About";

// 外观设置
"theme_color" = "Theme Color";
"dark_mode" = "Dark Mode";
"follow_system" = "Follow System";

// 语言设置
"app_language" = "App Language";
"system_language_settings" = "Go to System Settings";

// 播放设置
"auto_play" = "Auto Play";
"auto_play_desc" = "Automatically resume your last mix when opening the app";
"background_play" = "Background Play";
"background_play_desc" = "Continue playing sounds when you switch to other apps";
"lock_screen_control" = "Lock Screen Control";
"lock_screen_control_desc" = "Control playback directly from lock screen and control center without opening the app";

// Lock Screen Control VIP
"lock_screen_control_title" = "Lock Screen Control";
"lock_screen_control_vip_desc" = "Upgrade to VIP to control playback from lock screen and control center";
"lock_screen_control_feature" = "Lock Screen Control";
"lock_screen_control_feature_desc" = "Control playback from lock screen and control center";
"lock_screen_control_benefit1" = "Control playback without unlocking your device";
"lock_screen_control_benefit2" = "Quick access from control center";
"lock_screen_control_benefit3" = "Seamless integration with iOS media controls";

// Auto Play Messages
"auto_play_enabled" = "Auto Play Enabled";
"auto_play_disabled" = "Auto Play Disabled";
"no_last_mix_found" = "No last mix found";
"last_mix_loaded" = "Last mix loaded";
"auto_play_mix_name" = "Auto Play Mix";
"auto_play_mix_desc" = "Mix saved for auto play feature";

// Background Play Messages
"background_play_enabled" = "Background Play Enabled";
"background_play_disabled" = "Background Play Disabled";

// 关于
"version" = "Version";
"feedback" = "Feedback";
"feedback_desc" = "Send feedback and suggestions";
"rate_app" = "Rate App";
"rate_app_desc" = "Rate us on the App Store";

// MARK: - Timer
"timer" = "Timer";
"timer_remaining" = "%@ remaining";
"set_timer" = "Set Timer";
"timer_minutes" = "minutes";
"timer_hours" = "hours";

// MARK: - Alerts & Messages
"error" = "Error";
"success" = "Success";
"loading" = "Loading...";
"saved_successfully" = "Saved Successfully";
"deleted_successfully" = "Deleted Successfully";

// MARK: - Accessibility
"play_button" = "Play button";
"pause_button" = "Pause button";
"volume_slider" = "Volume slider";
"close_button" = "Close button";

// MARK: - VIP Subscription
// VIP Feature Titles
"vip_sound_title" = "VIP Exclusive Sounds";
"mix_limit_title" = "Unlimited Mixing";
"save_limit_title" = "Unlimited Favorites";
"vip_theme_title" = "VIP Exclusive Themes";
"long_timer_title" = "Extended Timer";

// VIP Feature Descriptions
"vip_sound_desc" = "This sound requires VIP membership to use. Upgrade to VIP to unlock all high-quality exclusive sounds!";
"mix_limit_desc" = "Free users can only play up to %d sounds simultaneously. Upgrade to VIP for unlimited mixing experience!";
"save_limit_desc" = "Free users can only save up to %d favorites. Upgrade to VIP for unlimited favorites!";
"vip_theme_desc" = "This theme requires VIP membership to use. Upgrade to VIP to unlock all beautiful themes!";
"long_timer_desc" = "Free users can only set timers up to 30 minutes. Upgrade to VIP for extended timer sessions!";

// VIP Benefits
"upgrade_will_get" = "After upgrading, you will get:";
"all_vip_benefits" = "All VIP Membership Benefits";

// VIP Sound Benefits
"vip_sound_benefit_1" = "Unlock all VIP exclusive sounds";
"vip_sound_benefit_2" = "High-quality audio experience";
"vip_sound_benefit_3" = "Exclusive sound content";

// Mix Limit Benefits
"mix_limit_benefit_1" = "Add unlimited sounds to mix";
"mix_limit_benefit_2" = "Create complex sound combinations";
"mix_limit_benefit_3" = "Professional mixing experience";

// Save Limit Benefits
"save_limit_benefit_1" = "Save unlimited favorites";
"save_limit_benefit_2" = "Create personal sound library";
"save_limit_benefit_3" = "Access favorite mixes anytime";

// VIP Theme Benefits
"vip_theme_benefit_1" = "Unlock all beautiful themes";
"vip_theme_benefit_2" = "Personalized interface experience";
"vip_theme_benefit_3" = "Exclusive visual design";



// Long Timer Benefits
"long_timer_benefit_1" = "Set timers longer than 30 minutes";
"long_timer_benefit_2" = "Extended relaxation sessions";
"long_timer_benefit_3" = "Perfect for sleep and meditation";

// VIP Feature List (for all features view)
"vip_sound_feature" = "VIP Exclusive Sounds";
"vip_sound_feature_desc" = "Unlock high-quality exclusive sounds";
"mix_limit_feature" = "Unlimited Mixing";
"mix_limit_feature_desc" = "Add unlimited sounds to mix";
"save_limit_feature" = "Unlimited Favorites";
"save_limit_feature_desc" = "Save unlimited favorites";
"vip_theme_feature" = "VIP Exclusive Themes";
"vip_theme_feature_desc" = "Use all beautiful themes";
"long_timer_feature" = "Extended Timer";
"long_timer_feature_desc" = "Set timers longer than 30 minutes";

// Action Buttons
"upgrade_to_vip" = "Upgrade to VIP";
"later_button" = "Later";
"tap_to_upgrade" = "Tap to Upgrade";

// MARK: - Theme Names
"theme_ocean_breeze" = "Ocean Breeze";
"theme_sunset_glow" = "Sunset Glow";
"theme_forest_mist" = "Forest Mist";
"theme_night_sky" = "Night Sky";
"theme_lavender_dream" = "Lavender Dream";
"theme_fire_ember" = "Fire Ember";

// MARK: - Theme Selector
"select_theme" = "Use This";
"current_theme" = "Current: %@";
"theme_in_use" = "In Use";
"previous_theme" = "Previous";
"next_theme" = "Next";

// Theme Confirmation
"confirm_theme_change" = "Confirm Theme Change";
"confirm_theme_change_message" = "Are you sure you want to switch to '%@' theme?";
"confirm" = "Confirm";

// Delete Favorite Confirmation
"delete_favorite_confirm" = "Delete Favorite Mix";
"delete_favorite_message" = "Deleting the last sound will also delete the favorite mix '%@'. Are you sure you want to continue?";
"delete_favorite" = "Delete Favorite";

// MARK: - Timer
"timer" = "Timer";
"time_remaining" = "Time Remaining";
"set_timer" = "Set Timer";
"timer_description" = "Choose playback duration, will auto-stop when time is up";
"quick_select" = "Quick Select";
"custom_time" = "Custom Time";
"hours" = "Hours";
"minutes" = "Minutes";
"seconds" = "Seconds";
"start_timer" = "Start Timer";
"cancel_timer" = "Cancel Timer";
"elapsed_time" = "Played %@";

// Timer durations
"timer_15min" = "15 Minutes";
"timer_30min" = "30 Minutes";
"timer_45min" = "45 Minutes";
"timer_1hour" = "1 Hour";
"timer_1_5hour" = "1.5 Hours";
"timer_2hour" = "2 Hours";

// Now Playing Info
"playing_count" = "Playing %d of %d sounds";
"active_sounds_count" = "%d active sounds";

// MARK: - Subscription Plans
"monthly_subscription" = "Monthly";
"yearly_subscription" = "Annual";
"lifetime_purchase" = "Lifetime";
"popular_badge" = "Popular";
"best_value_badge" = "Best Value";
"one_time_purchase" = "One-time purchase";
"cancel_anytime" = "Cancel anytime";
"save_over_30_percent" = "Save over 30% compared to monthly";
"lifetime_description" = "One purchase, enjoy all VIP features forever";
"choose_subscription_plan" = "Choose Subscription Plan";
"loading_subscription_options" = "Loading subscription options...";
"restore_purchases" = "Restore Purchases";
"terms_of_service" = "Terms of Service";
"privacy_policy" = "Privacy Policy";
"subscription_success" = "Subscription successful! Thank you for your support!";
"purchase_failed" = "Purchase failed: %@";
"restore_success" = "Purchases restored!";
"no_purchases_to_restore" = "No purchases found to restore";
"processing" = "Processing...";

// MARK: - Subscription Interface
"upgrade_to_vip_title" = "Upgrade to VIP";
"upgrade_subtitle" = "Unlock all premium features and enjoy the complete DIY Noise experience";
"vip_membership_benefits" = "VIP Membership Benefits";
"preview_mode_notice" = "Preview Mode - Please configure App Store Connect products";

// Subscription Period
"daily" = "Daily";
"weekly" = "Weekly";
"monthly" = "Monthly";
"yearly" = "Yearly";

// VIP Features
"vip_exclusive_sounds" = "VIP Exclusive Sounds";
"vip_exclusive_sounds_desc" = "Unlock all high-quality exclusive sounds";
"unlimited_mixing" = "Unlimited Mixing";
"unlimited_mixing_desc" = "Add unlimited sounds to your mix";
"unlimited_favorites" = "Unlimited Favorites";
"unlimited_favorites_desc" = "Save unlimited favorite mixes";
"vip_exclusive_themes" = "VIP Exclusive Themes";
"vip_exclusive_themes_desc" = "Use all beautiful interface themes";
"lock_screen_control_feature" = "Lock Screen Control";
"lock_screen_control_feature_desc" = "Control playback directly from lock screen and control center";
"extended_timer" = "Extended Timer";
"extended_timer_desc" = "Set timers longer than 30 minutes";

// Alert
"alert_title" = "Notice";
"ok_button" = "OK";

import SwiftUI
import StoreKit

// MARK: - Subscription View
struct SubscriptionView: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var themeManager = ThemeManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var isLoading = false
    @State private var purchasingProductId: String? = nil
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        ZStack {
            AnimatedGradientBackground()

            VStack(spacing: 0) {
                // Header
                headerView

                // Content
                ScrollView {
                    VStack(spacing: 24) {
                        // Hero Section
                        heroSection

                        // Subscription Plans
                        subscriptionPlansSection

                        // Features List
                        featuresSection

                        // Action Buttons
                        actionButtonsSection
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 40)
                }
            }

            // Global Loading Overlay
            if isLoading {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture { } // 防止点击穿透

                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                        .tint(.white)

                    Text("processing".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .padding(24)
                .background(Color.black.opacity(0.7))
                .cornerRadius(16)
                .transition(.scale.combined(with: .opacity))
            }
        }
        .alert("alert_title".localized, isPresented: $showAlert) {
            Button("ok_button".localized, role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .animation(.easeInOut(duration: 0.3), value: isLoading)
        .task {
            await subscriptionManager.loadProducts()
        }
    }
    
    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .themedText()
                    .frame(width: 44, height: 44)
                    .glassmorphism(cornerRadius: 12)
            }
            
            Spacer()
            
            VIPBadge(size: .medium, style: .crown)
            
            Spacer()
            
            Color.clear
                .frame(width: 44, height: 44)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
    
    private var heroSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(themeManager.accentColor.opacity(0.2))
                    .frame(width: 100, height: 100)
                
                Image(systemName: "crown.fill")
                    .font(.system(size: 40, weight: .medium))
                    .themedAccent()
            }
            
            VStack(spacing: 8) {
                Text("upgrade_to_vip_title".localized)
                    .font(.title)
                    .fontWeight(.bold)
                    .themedText()

                Text("upgrade_subtitle".localized)
                    .font(.subheadline)
                    .themedText()
                    .opacity(0.8)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var subscriptionPlansSection: some View {
        VStack(spacing: 12) {
            Text("choose_subscription_plan".localized)
                .font(.headline)
                .themedText()
                .frame(maxWidth: .infinity, alignment: .leading)

            if subscriptionManager.availableProducts.isEmpty {
                VStack(spacing: 12) {
                    ProgressView()
                        .tint(themeManager.accentColor)

                    Text("loading_subscription_options".localized)
                        .font(.subheadline)
                        .themedText()
                        .opacity(0.7)

                    // 显示模拟产品卡片用于预览
                    Text("preview_mode_notice".localized)
                        .font(.caption)
                        .themedText()
                        .opacity(0.5)
                }
                .frame(height: 120)
            } else {
                VStack(spacing: 12) {
                    ForEach(subscriptionManager.availableProducts, id: \.id) { product in
                        SubscriptionPlanCard(
                            product: product,
                            isSelected: false,
                            isLoading: purchasingProductId == product.id,
                            isDisabled: isLoading,
                            onTap: {
                                Task {
                                    await purchaseProduct(product)
                                }
                            }
                        )
                    }
                }
            }
        }
    }
    
    private var featuresSection: some View {
        VStack(spacing: 16) {
            Text("vip_membership_benefits".localized)
                .font(.headline)
                .themedText()
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                FeatureRow(
                    icon: "music.note",
                    title: "vip_exclusive_sounds".localized,
                    description: "vip_exclusive_sounds_desc".localized
                )

                FeatureRow(
                    icon: "infinity",
                    title: "unlimited_mixing".localized,
                    description: "unlimited_mixing_desc".localized
                )

                FeatureRow(
                    icon: "heart.fill",
                    title: "unlimited_favorites".localized,
                    description: "unlimited_favorites_desc".localized
                )

                FeatureRow(
                    icon: "paintbrush.fill",
                    title: "vip_exclusive_themes".localized,
                    description: "vip_exclusive_themes_desc".localized
                )

                FeatureRow(
                    icon: "lock.iphone",
                    title: "lock_screen_control_feature".localized,
                    description: "lock_screen_control_feature_desc".localized
                )

                FeatureRow(
                    icon: "timer",
                    title: "extended_timer".localized,
                    description: "extended_timer_desc".localized
                )
            }
        }
    }
    
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            // Restore Purchases Button
            Button(action: {
                Task {
                    await restorePurchases()
                }
            }) {
                Text("restore_purchases".localized)
                    .font(.system(size: 16, weight: .medium))
                    .themedText()
                    .opacity(0.7)
            }
            .disabled(isLoading)

            // Terms and Privacy
            HStack(spacing: 16) {
                Button("terms_of_service".localized) {
                    // TODO: 打开使用条款
                }

                Button("privacy_policy".localized) {
                    // TODO: 打开隐私政策
                }
            }
            .font(.caption)
            .themedText()
            .opacity(0.6)
        }
    }
    
    // MARK: - Actions
    
    private func purchaseProduct(_ product: Product) async {
        isLoading = true
        purchasingProductId = product.id

        do {
            let transaction = try await subscriptionManager.purchase(product)
            if transaction != nil {
                alertMessage = "subscription_success".localized
                showAlert = true

                // 延迟关闭界面
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    dismiss()
                }
            }
        } catch {
            alertMessage = String(format: "purchase_failed".localized, error.localizedDescription)
            showAlert = true
        }

        isLoading = false
        purchasingProductId = nil
    }
    
    private func restorePurchases() async {
        isLoading = true
        
        await subscriptionManager.restorePurchases()

        if subscriptionManager.isSubscribed {
            alertMessage = "restore_success".localized
        } else {
            alertMessage = "no_purchases_to_restore".localized
        }
        showAlert = true
        
        isLoading = false
    }
}

// MARK: - Subscription Plan Card
struct SubscriptionPlanCard: View {
    let product: Product
    let isSelected: Bool
    let isLoading: Bool
    let isDisabled: Bool
    let onTap: () -> Void

    @StateObject private var themeManager = ThemeManager.shared
    
    private var isPopular: Bool {
        // 年度订阅标记为热门
        return product.subscription?.subscriptionPeriod.unit == .year
    }

    private var isLifetime: Bool {
        // 检查是否为终生购买（非订阅类型）
        return product.subscription == nil
    }

    private var periodText: String {
        // 终生购买
        if isLifetime {
            return "one_time_purchase".localized
        }

        // 订阅类型
        guard let subscription = product.subscription else { return "" }

        switch subscription.subscriptionPeriod.unit {
        case .day:
            return "daily".localized
        case .week:
            return "weekly".localized
        case .month:
            return "monthly".localized
        case .year:
            return "yearly".localized
        @unknown default:
            return ""
        }
    }

    private var badgeText: String? {
        if isLifetime {
            return "best_value_badge".localized
        } else if isPopular {
            return "popular_badge".localized
        }
        return nil
    }

    private var badgeColor: Color {
        if isLifetime {
            return Color.purple
        } else if isPopular {
            return Color.orange
        }
        return Color.clear
    }

    private func getBorderColor() -> Color {
        if isLifetime {
            return Color.purple
        } else if isPopular {
            return themeManager.accentColor
        }
        return Color.clear
    }

    private var productDescription: String {
        // 优先使用商品的描述
        if !product.description.isEmpty {
            return product.description
        }

        // 如果商品没有描述，则使用周期文本作为后备
        return periodText
    }

    private func getMarketingText() -> String? {
        // 只有在商品没有描述或描述很短时，才显示额外的营销文本
        if product.description.isEmpty || product.description.count < 20 {
            if isLifetime {
                return "lifetime_description".localized
            } else if let subscription = product.subscription,
                      subscription.subscriptionPeriod.unit == .year {
                return "save_over_30_percent".localized
            } else {
                return "cancel_anytime".localized
            }
        }
        return nil
    }
    
    var body: some View {
        Button(action: {
            guard !isDisabled else { return }

            // 调试信息

            onTap()
        }) {
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(product.displayName)
                                .font(.headline)
                                .themedText()

                            if let badge = badgeText {
                                Text(badge)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(badgeColor)
                                    .cornerRadius(8)
                            }
                        }

                        // 显示商品的主要信息
                        if !product.description.isEmpty {
                            Text(product.description)
                                .font(.subheadline)
                                .themedText()
                                .opacity(0.7)
                                .lineLimit(2)
                        } else {
                            Text(periodText)
                                .font(.subheadline)
                                .themedText()
                                .opacity(0.7)
                        }
                    }
                    
                    Spacer()

                    if isLoading {
                        HStack(spacing: 8) {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(themeManager.accentColor)

                            Text("processing".localized)
                                .font(.subheadline)
                                .themedText()
                                .opacity(0.7)
                        }
                    } else {
                        Text(product.displayPrice)
                            .font(.title2)
                            .fontWeight(.bold)
                            .themedAccent()
                    }
                }
                
                // 额外的营销信息（仅在有特殊优势时显示）
//                if let marketingText = getMarketingText() {
//                    HStack {
//                        Text(marketingText)
//                            .font(.caption)
//                            .themedText()
//                            .opacity(0.8)
//
//                        Spacer()
//                    }
//                }
            }
            .padding(16)
            .glassmorphism(cornerRadius: 12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(getBorderColor(), lineWidth: 2)
            )
            .opacity(isDisabled && !isLoading ? 0.6 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isLoading)
            .animation(.easeInOut(duration: 0.2), value: isDisabled)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
    }
}

// MARK: - Feature Row
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    @StateObject private var themeManager = ThemeManager.shared
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .themedAccent()
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .themedText()
                
                Text(description)
                    .font(.caption)
                    .themedText()
                    .opacity(0.7)
            }
            
            Spacer()
            
            Image(systemName: "checkmark.circle.fill")
                .font(.caption)
                .foregroundColor(.green)
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    SubscriptionView()
}

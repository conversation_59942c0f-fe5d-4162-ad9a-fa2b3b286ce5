import SwiftUI

struct MixerView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var languageManager = LanguageManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var showSaveDialog = false
    @State private var showUpdateDialog = false
    @State private var showUpdateSuccessAlert = false
    @State private var showSubscriptionPrompt = false
    @State private var subscriptionFeature: SubscriptionPromptView.VIPFeature = .saveLimit
    @State private var mixName = ""
    @State private var mixDescription = ""
    @State private var savedMixes: [MixPreset] = []
    @State private var refreshTrigger = UUID()

    // 当前播放的收藏混音（直接使用 AudioManager 的状态）
    private var currentPlayingMix: MixPreset? {
        return audioManager.currentPlayingMix
    }

    // 检查是否有活跃的声音
    private var hasActiveSounds: Bool {
        // 监听音量更新触发器以确保实时更新
        _ = audioManager.volumeUpdateTrigger
        return !audioManager.activeSounds.isEmpty
    }

    // 检查是否有声音正在播放
    private var isAnyPlaying: Bool {
        // 监听音量更新触发器以确保实时更新
        _ = audioManager.volumeUpdateTrigger

        // 直接使用 AudioManager 的 isPlaying 状态，它会在 updatePlayingState 中正确计算
        return audioManager.isPlaying
    }
    
    var body: some View {
        ZStack {
            AnimatedGradientBackground()
            
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Content
                if audioManager.activeSounds.isEmpty {
                    emptyStateView
                } else {
                    activeSoundsView
                }
                
                Spacer()
                
                // Bottom Controls
                bottomControlsView
            }
        }
        .onAppear {
            loadSavedMixes()
        }
    }
    
    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .themedText()
                    .frame(width: 44, height: 44)
                    .glassmorphism(cornerRadius: 12)
            }
            
            Spacer()
            
            VStack {
                Text(L10nManager.mixerTitle)
                    .font(.title2)
                    .fontWeight(.bold)
                    .themedText()

                if let currentMix = currentPlayingMix {
                    Text(currentMix.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .themedAccent()
                }

                if !audioManager.activeSounds.isEmpty {
                    HStack(spacing: 8) {
                        Text(L10nManager.activeSoundsCount(audioManager.activeSounds.count))
                            .font(.caption)
                            .themedText()
                            .opacity(0.7)

                        // 显示限制状态（仅免费用户）
                        if !subscriptionManager.isSubscribed && !audioManager.activeSounds.isEmpty {
                            Text("/ \(SubscriptionManager.Limits.maxMixSoundsForFree)")
                                .font(.caption)
                                .themedAccent()
                                .opacity(0.8)
                        }
                    }
                }
            }
            
            Spacer()
            
            Button(action: {
                if hasActiveSounds {
                    // 如果有活跃声音，则暂停/恢复所有声音
                    if isAnyPlaying {
                        // 暂停所有声音
                        audioManager.pauseAllSounds()
                    } else {
                        // 播放所有声音
                        audioManager.resumeAllSounds()
                    }
                } else {
                    // 如果没有活跃声音，尝试播放当前混音（如果有的话）
                    if let currentMix = currentPlayingMix {
                        loadMix(currentMix)
                    }
                }
            }) {
                Image(systemName: isAnyPlaying ? "pause.circle.fill" : "play.circle.fill")
                    .font(.title2)
                    .themedAccent()
                    .opacity((!hasActiveSounds && currentPlayingMix == nil) ? 0.5 : 1.0)
                    .frame(width: 44, height: 44)
                    .glassmorphism(cornerRadius: 12)
            }
            .disabled(!hasActiveSounds && currentPlayingMix == nil)
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "slider.horizontal.3")
                .font(.system(size: 60))
                .themedAccent()
                .opacity(0.6)
            
            Text(L10nManager.noActiveSounds)
                .font(.title3)
                .fontWeight(.semibold)
                .themedText()

            Text(L10nManager.noActiveSoundsDesc)
                .font(.subheadline)
                .themedText()
                .opacity(0.7)
                .multilineTextAlignment(.center)
            
//            if !savedMixes.isEmpty {
//                VStack(spacing: 12) {
//                    Text("或加载已保存的混音")
//                        .font(.subheadline)
//                        .themedText()
//                        .opacity(0.7)
//                    
//                    ScrollView(.horizontal, showsIndicators: false) {
//                        HStack(spacing: 12) {
//                            ForEach(savedMixes) { mix in
//                                SavedMixCard(mix: mix) {
//                                    loadMix(mix)
//                                }
//                            }
//                        }
//                        .padding(.horizontal, 20)
//                    }
//                }
//            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var activeSoundsView: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Active Sounds
                LazyVStack(spacing: 12) {
                    ForEach(Array(audioManager.activeSounds.keys), id: \.self) { soundId in
                        if let player = audioManager.activeSounds[soundId] {
                            ActiveSoundControl(
                                soundId: soundId,
                                player: player,
                                onDeleteLastSound: {
                                    deleteCurrentFavorite()
                                }
                            )
                        }
                    }
                }
                
                // Saved Mixes
//                if !savedMixes.isEmpty {
//                    savedMixesSection
//                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
        }
    }
    

    
    private var savedMixesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(L10nManager.savedMixes)
                .font(.headline)
                .themedText()
                .padding(.horizontal, 4)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(savedMixes) { mix in
                        SavedMixCard(mix: mix) {
                            loadMix(mix)
                        }
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
    
    private var bottomControlsView: some View {
        VStack(spacing: 16) {
            if !audioManager.activeSounds.isEmpty {
                if currentPlayingMix != nil {
                    // 情况1：当前播放基于收藏混音（包括在收藏基础上添加了新声音）- 显示两个按钮
                    HStack(spacing: 16) {
                        Button(action: { showUpdateDialog = true }) {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                Text(L10nManager.updateCurrentMix)
                            }
                            .font(.system(size: 16, weight: .medium))
                            .themedText()
                            .frame(height: 50)
                            .frame(maxWidth: .infinity)
                            .glassmorphism(cornerRadius: 25)
                        }

                        Button(action: { checkSaveLimitAndShowDialog() }) {
                            HStack {
                                Image(systemName: "plus.circle")
                                Text(L10nManager.saveAsNewMix)
                            }
                            .font(.system(size: 16, weight: .medium))
                            .themedText()
                            .frame(height: 50)
                            .frame(maxWidth: .infinity)
                            .glassmorphism(cornerRadius: 25)
                        }
                    }
                    .padding(.horizontal, 20)
                } else {
                    // 情况2：不是播放收藏混音 - 只显示保存按钮
                    Button(action: { checkSaveLimitAndShowDialog() }) {
                        HStack {
                            Image(systemName: "square.and.arrow.down")
                            Text(L10nManager.saveMix)
                        }
                        .font(.system(size: 16, weight: .medium))
                        .themedText()
                        .frame(height: 50)
                        .frame(maxWidth: .infinity)
                        .glassmorphism(cornerRadius: 25)
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .padding(.bottom, 20)
        .alert(L10nManager.saveMix, isPresented: $showSaveDialog) {
            TextField(L10nManager.mixNamePlaceholder, text: $mixName)
            TextField(L10nManager.mixDescriptionPlaceholder, text: $mixDescription)
            Button(L10nManager.save) { saveMix() }
            Button(L10nManager.cancel, role: .cancel) { }
        } message: {
            Text(L10nManager.mixNamePrompt)
        }
        .alert(L10nManager.updateMix, isPresented: $showUpdateDialog) {
            Button(L10nManager.updateMix) { updateCurrentMix() }
            Button(L10nManager.cancel, role: .cancel) { }
        } message: {
            Text(L10nManager.updateMixPrompt)
        }
        .alert("更新成功", isPresented: $showUpdateSuccessAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            if let currentMix = currentPlayingMix {
                Text("收藏混音「\(currentMix.name)」已成功更新")
            }
        }
        .sheet(isPresented: $showSubscriptionPrompt) {
            SubscriptionPromptView(feature: subscriptionFeature)
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger = UUID()
        }
        .id(refreshTrigger)
    }

    private func checkSaveLimitAndShowDialog() {
        // 检查收藏数量限制
        if !subscriptionManager.canSaveMoreMixes(currentCount: savedMixes.count) {
            subscriptionFeature = .saveLimit
            showSubscriptionPrompt = true
            return
        }

        // 如果没有限制，显示保存对话框
        showSaveDialog = true
    }

    private func saveMix() {
        let mix = audioManager.saveMix(name: mixName.isEmpty ? L10nManager.unnamedMix : mixName,
                                     description: mixDescription)
        savedMixes.append(mix)
        saveMixesToUserDefaults()

        // 保存为自动播放的混音
        SettingsManager.shared.saveLastPlayedMix(mix)

        mixName = ""
        mixDescription = ""

        // 发送通知，通知其他页面混音已保存
        NotificationCenter.default.post(name: .mixSaved, object: nil)
    }

    private func updateCurrentMix() {
        guard let currentMix = currentPlayingMix else { return }

        // 获取当前混音器中的声音数据
        let currentSoundMixes = audioManager.activeSounds.map { (soundId, player) in
            SoundMix(soundId: soundId, volume: player.volume, isEnabled: true)
        }

        // 创建更新的混音数据，保持原来的 ID 和创建时间
        let updatedMix = MixPreset(
            id: currentMix.id,
            name: currentMix.name,
            description: currentMix.description,
            soundMixes: currentSoundMixes,
            createdAt: currentMix.createdAt
        )

        // 找到并更新现有混音
        if let index = savedMixes.firstIndex(where: { $0.id == currentMix.id }) {
            // 替换为更新后的混音
            savedMixes[index] = updatedMix

            saveMixesToUserDefaults()

            // 保存为自动播放的混音
            SettingsManager.shared.saveLastPlayedMix(updatedMix)

            // 更新 AudioManager 中的当前播放混音引用
            audioManager.currentPlayingMix = updatedMix

            // 发送通知，通知其他页面混音已更新
            NotificationCenter.default.post(name: .mixSaved, object: nil)

            // 显示成功提示
            showUpdateSuccessAlert = true
        }
    }
    
    private func loadMix(_ mix: MixPreset) {
        let allSounds = SoundCategory.sampleCategories.flatMap { $0.sounds }
        audioManager.loadMix(mix, sounds: allSounds)
    }
    
    private func loadSavedMixes() {
        if let data = UserDefaults.standard.data(forKey: "savedMixes"),
           let mixes = try? JSONDecoder().decode([MixPreset].self, from: data) {
            savedMixes = mixes
        }
    }
    
    private func saveMixesToUserDefaults() {
        if let data = try? JSONEncoder().encode(savedMixes) {
            UserDefaults.standard.set(data, forKey: "savedMixes")
        }
    }

    private func deleteCurrentFavorite() {
        guard let currentMix = audioManager.currentPlayingMix else { return }



        // 从保存的混音列表中删除
        savedMixes.removeAll { $0.id == currentMix.id }
        saveMixesToUserDefaults()

        // 清除 AudioManager 中的当前播放混音引用
        audioManager.currentPlayingMix = nil

        // 发送通知，通知其他页面混音已删除
        NotificationCenter.default.post(name: .mixSaved, object: nil)


    }
}

struct ActiveSoundControl: View {
    let soundId: UUID
    let player: any SoundPlayerProtocol
    let onDeleteLastSound: (() -> Void)?  // 删除最后一个声音的回调
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showDeleteFavoriteAlert = false
    @State private var showSubscriptionPrompt = false

    private var currentVolume: Float {
        // 监听 volumeUpdateTrigger 来确保UI更新
        _ = audioManager.volumeUpdateTrigger
        return audioManager.activeSounds[soundId]?.volume ?? player.volume
    }

    // 获取对应的声音项
    private var soundItem: SoundItem? {
        let allSounds = SoundCategory.sampleCategories.flatMap { $0.sounds }
        return allSounds.first(where: { $0.id == soundId })
    }

    // 检查这个声音是否是 VIP 声音
    private var isVIPSound: Bool {
        return soundItem?.isVIP ?? false
    }

    // 检查这个声音是否被 VIP 限制（VIP 声音但用户未订阅）
    private var isVIPLocked: Bool {
        return isVIPSound && !subscriptionManager.isSubscribed
    }



    var body: some View {
        VStack(spacing: 12) {
            HStack {
                // Sound name with VIP badge
                HStack(spacing: 8) {
                    Text(player.soundName)
                        .font(.headline)
                        .themedText()
                        .opacity(isVIPLocked ? 0.6 : 1.0)  // VIP 锁定时显示为半透明

                    // 始终显示 VIP 标识（如果是 VIP 声音）
                    if isVIPSound {
                        VIPBadge(size: .small, style: .crown)
                    }
                }

                Spacer()

                // Remove
                Button(action: {
                    // 检查是否是收藏混音的最后一个声音
                    if audioManager.currentPlayingMix != nil && audioManager.activeSounds.count == 1 {
                        // 如果是最后一个声音，显示删除收藏的确认对话框
                        showDeleteFavoriteAlert = true
                    } else {
                        // 正常删除声音
                        audioManager.stopSound(soundId)
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.red)
                }
            }

            // Volume Control
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: isVIPLocked ? "speaker.slash.fill" : "speaker.fill")
                        .font(.caption)
                        .themedAccent()
                        .opacity(isVIPLocked ? 0.5 : 1.0)

                    Slider(value: Binding(
                        get: { currentVolume },
                        set: { newValue in
                            if !isVIPLocked {
                                audioManager.setVolume(for: soundId, volume: newValue)
                            }
                        }
                    ), in: 0...1)
                    .accentColor(themeManager.accentColor)
                    .disabled(isVIPLocked)  // VIP 锁定时禁用音量控制

                    Text(isVIPLocked ? "VIP" : "\(Int(currentVolume * 100))%")
                        .font(.caption)
                        .themedAccent()
                        .opacity(isVIPLocked ? 0.5 : 1.0)
                        .frame(width: 35)
                }
            }
        }
        .padding(16)
        .glassmorphism(cornerRadius: 16)
        .overlay(
            // VIP 锁定遮罩 - 只有在 VIP 锁定时显示
            VIPLockOverlay(isLocked: isVIPLocked, size: .large) {
                showSubscriptionPrompt = true
            }
        )
        .alert(L10nManager.deleteFavoriteConfirm, isPresented: $showDeleteFavoriteAlert) {
            Button(L10nManager.cancel, role: .cancel) { }
            Button(L10nManager.deleteFavorite, role: .destructive) {
                // 删除声音
                audioManager.stopSound(soundId)
                // 通知父视图删除收藏
                onDeleteLastSound?()
            }
        } message: {
            if let currentMix = audioManager.currentPlayingMix {
                Text(L10nManager.deleteFavoriteMessage(currentMix.name))
            }
        }
        .sheet(isPresented: $showSubscriptionPrompt) {
            SubscriptionPromptView(feature: .vipSound)
        }
        .onReceive(subscriptionManager.$isSubscribed) { isSubscribed in
            // 当订阅状态变化时，控制 VIP 声音的播放状态
            if let sound = soundItem, sound.isVIP {
                if isSubscribed {
                    // 订阅后，如果声音应该播放则恢复播放
                    if audioManager.isPlaying {
                        player.play()
                    }
                } else {
                    // 取消订阅后，静音 VIP 声音
                    player.pause()
                }
            }
        }
    }
}

struct SavedMixCard: View {
    let mix: MixPreset
    let action: () -> Void
    @StateObject private var themeManager = ThemeManager.shared

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Text(mix.name)
                    .font(.headline)
                    .themedText()
                    .lineLimit(1)

                Text(mix.description)
                    .font(.caption)
                    .themedText()
                    .opacity(0.7)
                    .lineLimit(2)

                Text(L10nManager.mixSoundsCount(mix.soundMixes.count))
                    .font(.caption2)
                    .themedAccent()
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(themeManager.accentColor.opacity(0.2))
                    )
            }
            .padding(12)
            .frame(width: 140, height: 100)
            .glassmorphism(cornerRadius: 12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    MixerView()
}

import SwiftUI

// MARK: - Subscription Prompt View
struct SubscriptionPromptView: View {
    let feature: VIPFeature
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var languageManager = LanguageManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var showFullSubscriptionView = false
    
    enum VIPFeature {
        case vipSound
        case mixLimit
        case saveLimit
        case vipTheme
        case lockScreenControl
        case longTimer
        
        var title: String {
            switch self {
            case .vipSound: return L10nManager.vipSoundTitle
            case .mixLimit: return L10nManager.mixLimitTitle
            case .saveLimit: return L10nManager.saveLimitTitle
            case .vipTheme: return L10nManager.vipThemeTitle
            case .lockScreenControl: return L10nManager.lockScreenControlTitle
            case .longTimer: return L10nManager.longTimerTitle
            }
        }
        
        var description: String {
            switch self {
            case .vipSound: return L10nManager.vipSoundDesc
            case .mixLimit: return L10nManager.mixLimitDesc(SubscriptionManager.Limits.maxMixSoundsForFree)
            case .saveLimit: return L10nManager.saveLimitDesc(SubscriptionManager.Limits.maxSavedMixesForFree)
            case .vipTheme: return L10nManager.vipThemeDesc
            case .lockScreenControl: return L10nManager.lockScreenControlVipDesc
            case .longTimer: return L10nManager.longTimerDesc
            }
        }
        
        var icon: String {
            switch self {
            case .vipSound: return "music.note"
            case .mixLimit: return "infinity"
            case .saveLimit: return "heart.fill"
            case .vipTheme: return "paintbrush.fill"
            case .lockScreenControl: return "lock.iphone"
            case .longTimer: return "timer"
            }
        }
        
        var benefits: [String] {
            switch self {
            case .vipSound:
                return [
                    L10nManager.vipSoundBenefit1,
                    L10nManager.vipSoundBenefit2,
                    L10nManager.vipSoundBenefit3
                ]
            case .mixLimit:
                return [
                    L10nManager.mixLimitBenefit1,
                    L10nManager.mixLimitBenefit2,
                    L10nManager.mixLimitBenefit3
                ]
            case .saveLimit:
                return [
                    L10nManager.saveLimitBenefit1,
                    L10nManager.saveLimitBenefit2,
                    L10nManager.saveLimitBenefit3
                ]
            case .vipTheme:
                return [
                    L10nManager.vipThemeBenefit1,
                    L10nManager.vipThemeBenefit2,
                    L10nManager.vipThemeBenefit3
                ]
            case .lockScreenControl:
                return [
                    L10nManager.lockScreenControlBenefit1,
                    L10nManager.lockScreenControlBenefit2,
                    L10nManager.lockScreenControlBenefit3
                ]
            case .longTimer:
                return [
                    L10nManager.longTimerBenefit1,
                    L10nManager.longTimerBenefit2,
                    L10nManager.longTimerBenefit3
                ]
            }
        }
    }
    
    var body: some View {
        ZStack {
            AnimatedGradientBackground()
            
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Content
                ScrollView {
                    VStack(spacing: 24) {
                        // Feature Icon and Title
                        featureHeaderView
                        
                        // Description
                        descriptionView
                        
                        // Benefits
                        benefitsView
                        
                        // All VIP Features
                        allFeaturesView
                        
                        // Action Buttons
                        actionButtonsView
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 40)
                }
            }
        }
    }
    
    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .themedText()
                    .frame(width: 44, height: 44)
                    .glassmorphism(cornerRadius: 12)
            }
            
            Spacer()
            
            VIPBadge(size: .medium, style: .crown)
            
            Spacer()
            
            Color.clear
                .frame(width: 44, height: 44)
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var featureHeaderView: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(themeManager.accentColor.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: feature.icon)
                    .font(.system(size: 32, weight: .medium))
                    .themedAccent()
            }
            
            Text(feature.title)
                .font(.title)
                .fontWeight(.bold)
                .themedText()
                .multilineTextAlignment(.center)
        }
    }
    
    private var descriptionView: some View {
        Text(feature.description)
            .font(.body)
            .themedText()
            .opacity(0.8)
            .multilineTextAlignment(.center)
            .lineLimit(nil)
            .padding(.horizontal, 10)
    }
    
    private var benefitsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(L10nManager.upgradeWillGet)
                .font(.headline)
                .themedText()
            
            VStack(spacing: 12) {
                ForEach(feature.benefits, id: \.self) { benefit in
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title3)
                            .foregroundColor(.green)
                        
                        Text(benefit)
                            .font(.subheadline)
                            .themedText()
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(20)
        .glassmorphism(cornerRadius: 16)
    }
    
    private var allFeaturesView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(L10nManager.allVipBenefits)
                .font(.headline)
                .themedText()
            
            VStack(spacing: 12) {
                VIPFeatureRow(
                    icon: "music.note",
                    title: L10nManager.vipSoundFeature,
                    description: L10nManager.vipSoundFeatureDesc,
                    isHighlighted: feature == .vipSound
                )

                VIPFeatureRow(
                    icon: "infinity",
                    title: L10nManager.mixLimitFeature,
                    description: L10nManager.mixLimitFeatureDesc,
                    isHighlighted: feature == .mixLimit
                )

                VIPFeatureRow(
                    icon: "heart.fill",
                    title: L10nManager.saveLimitFeature,
                    description: L10nManager.saveLimitFeatureDesc,
                    isHighlighted: feature == .saveLimit
                )

                VIPFeatureRow(
                    icon: "paintbrush.fill",
                    title: L10nManager.vipThemeFeature,
                    description: L10nManager.vipThemeFeatureDesc,
                    isHighlighted: feature == .vipTheme
                )

                VIPFeatureRow(
                    icon: "lock.iphone",
                    title: L10nManager.lockScreenControlFeature,
                    description: L10nManager.lockScreenControlFeatureDesc,
                    isHighlighted: feature == .lockScreenControl
                )

                VIPFeatureRow(
                    icon: "timer",
                    title: L10nManager.longTimerFeature,
                    description: L10nManager.longTimerFeatureDesc,
                    isHighlighted: feature == .longTimer
                )
            }
        }
        .padding(20)
        .glassmorphism(cornerRadius: 16)
    }
    
    private var actionButtonsView: some View {
        VStack(spacing: 16) {
            // Upgrade Button
            Button(action: {
                showFullSubscriptionView = true
            }) {
                HStack {
                    Image(systemName: "crown.fill")
                    Text(L10nManager.upgradeToVip)
                }
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(height: 56)
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.yellow.opacity(0.8), Color.orange.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(28)
                .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
            }
            
            // Cancel Button
            Button(action: { dismiss() }) {
                Text(L10nManager.laterButton)
                    .font(.system(size: 16, weight: .medium))
                    .themedText()
                    .opacity(0.7)
            }
        }
        .fullScreenCover(isPresented: $showFullSubscriptionView) {
            SubscriptionView()
        }
    }
}

struct VIPFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    let isHighlighted: Bool
    
    @StateObject private var themeManager = ThemeManager.shared
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(isHighlighted ? themeManager.accentColor : .green)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(isHighlighted ? .semibold : .medium)
                    .themedText()
                
                Text(description)
                    .font(.caption)
                    .themedText()
                    .opacity(0.7)
            }
            
            Spacer()
            
            if isHighlighted {
                Image(systemName: "star.fill")
                    .font(.caption)
                    .foregroundColor(themeManager.accentColor)
            } else {
                Image(systemName: "checkmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isHighlighted ? themeManager.accentColor.opacity(0.1) : Color.clear)
        )
    }
}

#Preview {
    SubscriptionPromptView(feature: .vipSound)
}

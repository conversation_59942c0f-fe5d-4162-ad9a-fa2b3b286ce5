import SwiftUI

struct SettingsView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var settingsManager = SettingsManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showThemeSelector = false

    @State private var showSubscriptionPrompt = false
    @State private var subscriptionFeature: SubscriptionPromptView.VIPFeature = .lockScreenControl
    @State private var refreshTrigger = UUID()
    
    var body: some View {
        ZStack {
            // Background
            AnimatedGradientBackground()

            VStack(spacing: 0) {
                // Header
                headerView

                // Settings Content
                ScrollView {
                    VStack(spacing: 20) {
                        // 主题设置
                        themeSection

                        // 播放设置
                        playbackSection

                        // 关于应用
                        aboutSection

                        // 开发测试 (仅在 Debug 模式下显示)
                        #if DEBUG
                        developmentSection
                        #endif
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 120) // 为Tab栏和播放控制器留出更多空间
                }
            }
        }
        .sheet(isPresented: $showThemeSelector) {
            ThemeSelectorView()
        }

        .sheet(isPresented: $showSubscriptionPrompt) {
            SubscriptionPromptView(feature: subscriptionFeature)
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger = UUID()
        }
        .id(refreshTrigger)
    }
    
    private var headerView: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(L10nManager.settingsTitle)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .themedText()

                    Text(L10nManager.settingsSubtitle)
                        .font(.subheadline)
                        .themedText()
                        .opacity(0.8)
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
        }
    }
    
    private var themeSection: some View {
        SettingsSection(title: L10nManager.appearanceSection, icon: "gearshape") {
            VStack(spacing: 12) {
                SettingsRow(
                    title: L10nManager.themeColor,
                    subtitle: themeManager.currentTheme.name,
                    icon: "paintbrush.fill",
                    iconColor: themeManager.accentColor
                ) {
                    showThemeSelector = true
                }
                
                SettingsRow(
                    title: L10nManager.appLanguage,
                    subtitle: L10nManager.systemLanguageSettings,
                    icon: "globe"
                ) {
                    openSystemLanguageSettings()
                }
            }
        }
    }

    private var playbackSection: some View {
        SettingsSection(title: L10nManager.playbackSection, icon: "speaker.wave.2.fill") {
            VStack(spacing: 12) {
                SettingsToggleRow(
                    title: L10nManager.autoPlay,
                    subtitle: L10nManager.autoPlayDesc,
                    icon: "play.circle",
                    isOn: $settingsManager.autoPlay
                )

                SettingsToggleRow(
                    title: L10nManager.backgroundPlay,
                    subtitle: L10nManager.backgroundPlayDesc,
                    icon: "app.badge",
                    isOn: $settingsManager.backgroundPlay
                )

                VIPToggleRow(
                    title: L10nManager.lockScreenControl,
                    subtitle: L10nManager.lockScreenControlDesc,
                    icon: "lock.iphone",
                    isOn: $settingsManager.lockScreenControl,
                    requiresVIP: true,
                    vipFeature: .lockScreenControl
                ) {
                    subscriptionFeature = .lockScreenControl
                    showSubscriptionPrompt = true
                }
            }
        }
    }
    
    private var aboutSection: some View {
        SettingsSection(title: L10nManager.aboutSection, icon: "info.circle") {
            VStack(spacing: 12) {
                SettingsRow(
                    title: L10nManager.version,
                    subtitle: "1.0.0",
                    icon: "app.badge"
                ) { }

                SettingsRow(
                    title: L10nManager.feedback,
                    subtitle: L10nManager.feedbackDesc,
                    icon: "envelope"
                ) {
                    // 打开邮件应用
                }

                SettingsRow(
                    title: L10nManager.rateApp,
                    subtitle: L10nManager.rateAppDesc,
                    icon: "star"
                ) {
                    // 打开App Store评分
                }
            }
        }
    }

    #if DEBUG
    private var developmentSection: some View {
        SettingsSection(title: "开发测试", icon: "hammer.fill") {
            VStack(spacing: 12) {
                SettingsRow(
                    title: "订阅功能测试",
                    subtitle: "测试 VIP 功能开关",
                    icon: "crown.fill",
                    iconColor: .yellow
                ) {
                    // Test functionality removed
                }
            }
        }
    }
    #endif

    // MARK: - Helper Methods
    private func openSystemLanguageSettings() {
        // 尝试打开应用的语言设置页面
        if let appSettingsURL = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(appSettingsURL) {
                UIApplication.shared.open(appSettingsURL)
            }
        }
    }
}

// MARK: - Settings Components
struct SettingsSection<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title3)
                    .themedAccent()
                
                Text(title)
                    .font(.headline)
                    .themedText()
            }
            .padding(.horizontal, 4)
            
            VStack(spacing: 1) {
                content
            }
            .glassmorphism(cornerRadius: 12)
        }
    }
}

struct SettingsRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let iconColor: Color?
    let action: () -> Void
    
    init(title: String, subtitle: String, icon: String, iconColor: Color? = nil, action: @escaping () -> Void) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.iconColor = iconColor
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor ?? ThemeManager.shared.accentColor)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .themedText()
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(subtitle)
                        .font(.caption)
                        .themedText()
                        .opacity(0.7)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .themedText()
                    .opacity(0.5)
            }
            .padding(12)
            .frame(maxWidth: .infinity, alignment: .leading)  // 确保按钮占满整个宽度
            .contentShape(Rectangle())  // 让整个矩形区域都可以点击
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SettingsToggleRow: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool
    
    var body: some View {
        Button(action: {
            isOn.toggle()  // 点击整个区域切换开关
        }) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .themedAccent()
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .themedText()
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(subtitle)
                        .font(.caption)
                        .themedText()
                        .opacity(0.7)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                Toggle("", isOn: $isOn)
                    .tint(ThemeManager.shared.accentColor)
                    .allowsHitTesting(false)  // 禁用 Toggle 自身的点击，让外层 Button 处理
            }
            .padding(12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SettingsSliderRow: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var value: Float
    
    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .themedAccent()
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .themedText()
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(subtitle)
                        .font(.caption)
                        .themedAccent()
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }

            Slider(value: $value, in: 0...1)
                .tint(ThemeManager.shared.accentColor)
                .padding(.horizontal, 36)
        }
        .padding(12)
        .frame(maxWidth: .infinity, alignment: .leading)  // 确保占满整个宽度
    }
}

struct VIPToggleRow: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool
    let requiresVIP: Bool
    let vipFeature: SubscriptionPromptView.VIPFeature
    let onVIPRequired: () -> Void

    @StateObject private var subscriptionManager = SubscriptionManager.shared

    private var canUseFeature: Bool {
        !requiresVIP || subscriptionManager.isSubscribed
    }

    // 使用与应用其他地方一致的VIP标签样式
    private var badgeStyle: VIPBadge.VIPBadgeStyle {
        return .crown  // 统一使用crown样式，与其他VIP功能保持一致
    }

    var body: some View {
        Button(action: {
            if canUseFeature {
                isOn.toggle()
            } else {
                onVIPRequired()
            }
        }) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .themedAccent()
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(title)
                            .font(.body)
                            .themedText()

                        if requiresVIP {
                            VIPBadge(size: .small, style: badgeStyle)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    Text(subtitle)
                        .font(.caption)
                        .themedText()
                        .opacity(canUseFeature ? 0.7 : 0.5)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                if canUseFeature {
                    Toggle("", isOn: $isOn)
                        .tint(ThemeManager.shared.accentColor)
                        .allowsHitTesting(false)
                } else {
                    Image(systemName: "lock.fill")
                        .font(.caption)
                        .themedText()
                        .opacity(0.5)
                }
            }
            .padding(12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .contentShape(Rectangle())
            .opacity(canUseFeature ? 1.0 : 0.6)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    SettingsView()
        .background(Color.blue.gradient)
}

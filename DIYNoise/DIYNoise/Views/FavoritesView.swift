import SwiftUI

extension Notification.Name {
    static let mixSaved = Notification.Name("mixSaved")
}

struct FavoritesView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var languageManager = LanguageManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var savedMixes: [MixPreset] = []
    @State private var showDeleteAlert = false
    @State private var mixToDelete: MixPreset?
    @State private var showSubscriptionPrompt = false
    @State private var refreshTrigger = UUID()
    
    var body: some View {
        ZStack {
            // Background
            AnimatedGradientBackground()

            VStack(spacing: 0) {
                // Header
                headerView

                // Content
                if savedMixes.isEmpty {
                    emptyStateView
                } else {
                    mixesListView
                }
            }
        }
        .onAppear {
            loadSavedMixes()
        }
        .onReceive(NotificationCenter.default.publisher(for: .mixSaved)) { _ in
            loadSavedMixes()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger = UUID()
        }
        .id(refreshTrigger)
        .alert(L10nManager.deleteFavorite, isPresented: $showDeleteAlert) {
            Button(L10nManager.delete, role: .destructive) {
                if let mix = mixToDelete {
                    deleteMix(mix)
                }
            }
            Button(L10nManager.cancel, role: .cancel) { }
        } message: {
            Text(L10nManager.deleteFavoriteMessage)
        }
        .sheet(isPresented: $showSubscriptionPrompt) {
            SubscriptionPromptView(feature: .saveLimit)
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(L10nManager.favoritesTitle)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .themedText()

                    Text(L10nManager.favoritesSubtitle)
                        .font(.subheadline)
                        .themedText()
                        .opacity(0.8)
                }
                
                Spacer()
                
                // 收藏数量
                if !savedMixes.isEmpty {
                    VStack(alignment: .trailing, spacing: 4) {
                        HStack(spacing: 4) {
                            Text("\(savedMixes.count)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .themedAccent()

                            // 显示限制（仅免费用户）
                            if !subscriptionManager.isSubscribed {
                                Text("/ \(SubscriptionManager.Limits.maxSavedMixesForFree)")
                                    .font(.title3)
                                    .fontWeight(.medium)
                                    .themedText()
                                    .opacity(0.6)
                            }
                        }

                        Text(L10nManager.favoritesCount)
                            .font(.caption)
                            .themedText()
                            .opacity(0.7)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "heart")
                .font(.system(size: 60))
                .themedAccent()
                .opacity(0.6)
            
            VStack(spacing: 8) {
                Text(L10nManager.noFavorites)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .themedText()

                Text(L10nManager.noFavoritesDesc)
                    .font(.subheadline)
                    .themedText()
                    .opacity(0.7)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var mixesListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(Array(savedMixes.enumerated()), id: \.element.id) { index, mix in
                    FavoriteMixCard(
                        mix: mix,
                        mixIndex: index,  // 传递索引用于权限检查
                        onPlay: {
                            // 检查是否正在播放这个特定的混音
                            if audioManager.currentPlayingMix?.id == mix.id && audioManager.isPlaying {
                                // 如果正在播放这个混音，则停止
                                audioManager.stopAllSounds()
                            } else {
                                // 如果没有播放这个混音，则加载并播放
                                loadMix(mix)
                            }
                        },
                        onDelete: {
                            mixToDelete = mix
                            showDeleteAlert = true
                        },
                        onVIPTap: {
                            // 显示 VIP 订阅提示
                            showSubscriptionPrompt = true
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 120) // 为Tab栏和播放控制器留出更多空间
        }
    }
    
    private func loadSavedMixes() {
        if let data = UserDefaults.standard.data(forKey: "savedMixes"),
           let mixes = try? JSONDecoder().decode([MixPreset].self, from: data) {
            // 按创建时间正序排列，这样前5个是最早创建的（免费用户可用）
            savedMixes = mixes.sorted { $0.createdAt < $1.createdAt }
        }
    }
    
    private func deleteMix(_ mix: MixPreset) {
        savedMixes.removeAll { $0.id == mix.id }
        saveMixesToUserDefaults()
    }
    
    private func loadMix(_ mix: MixPreset) {
        let allSounds = SoundCategory.sampleCategories.flatMap { $0.sounds }
        audioManager.loadMix(mix, sounds: allSounds)
    }
    
    private func saveMixesToUserDefaults() {
        if let data = try? JSONEncoder().encode(savedMixes) {
            UserDefaults.standard.set(data, forKey: "savedMixes")
        }
    }
}

struct FavoriteMixCard: View {
    let mix: MixPreset
    let mixIndex: Int  // 收藏在列表中的索引
    let onPlay: () -> Void
    let onDelete: () -> Void
    let onVIPTap: (() -> Void)?  // VIP 遮罩点击回调

    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared

    // 检查这个混音是否正在播放
    private var isCurrentlyPlaying: Bool {
        // 监听音量更新触发器以确保实时更新
        _ = audioManager.volumeUpdateTrigger

        // 检查当前播放的混音ID是否匹配
        guard audioManager.currentPlayingMix?.id == mix.id else {
            return false
        }

        // 检查是否有声音在播放
        guard audioManager.isPlaying else {
            return false
        }

        // 检查当前播放的声音是否与原始混音完全匹配
        let playingSoundIds = Set(audioManager.activeSounds.keys)
        let originalMixSoundIds = Set(mix.soundMixes.map { $0.soundId })

        // 如果声音完全匹配，则显示为正在播放原始混音
        // 如果声音不匹配，则显示为已修改状态（不是纯粹的播放状态）
        return playingSoundIds == originalMixSoundIds
    }

    // 获取播放中的声音数量
    private var playingSoundCount: Int {
        mix.soundMixes.filter { soundMix in
            audioManager.activeSounds[soundMix.soundId]?.isPlaying ?? false
        }.count
    }

    // 检查这个收藏是否需要 VIP 权限
    private var isLocked: Bool {
        // 如果已经订阅，所有收藏都可以使用
        if subscriptionManager.isSubscribed {
            return false
        }

        // 免费用户只能使用前 5 个收藏（按创建时间排序）
        return mixIndex >= SubscriptionManager.Limits.maxSavedMixesForFree
    }
    
    var body: some View {
        HStack(spacing: 16) {
            // Mix Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(mix.name)
                        .font(.headline)
                        .themedText()
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Text(formatDate(mix.createdAt))
                        .font(.caption2)
                        .themedText()
                        .opacity(0.6)
                }
                
                Text(mix.description)
                    .font(.subheadline)
                    .themedText()
                    .opacity(0.8)
                    .lineLimit(2)
                
                HStack(spacing: 12) {
                    if isCurrentlyPlaying {
                        Label(L10nManager.playingCount(playingSoundCount, mix.soundMixes.count), systemImage: "waveform")
                            .font(.caption)
                            .foregroundColor(themeManager.accentColor)
                            .background(
                                Capsule()
                                    .fill(themeManager.accentColor.opacity(0.2))
                                    .overlay(
                                        Capsule()
                                            .stroke(themeManager.accentColor.opacity(0.5), lineWidth: 1)
                                            .scaleEffect(1.1)
                                            .opacity(0.8)
                                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isCurrentlyPlaying)
                                    )
                            )
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                    } else {
                        Label(L10nManager.mixSoundsCount(mix.soundMixes.count), systemImage: "waveform")
                            .font(.caption)
                            .themedAccent()
                    }

                    Label(L10nManager.mixCombination, systemImage: "slider.horizontal.3")
                        .font(.caption)
                        .themedAccent()
                }
            }
            
            Spacer()
            
            // Action Buttons
            VStack(spacing: 8) {
                Button(action: {
                    if isLocked {
                        // 如果被锁定，触发 VIP 提示
                        onVIPTap?()
                    } else {
                        // 正常播放
                        onPlay()
                    }
                }) {
                    ZStack {
                        if isCurrentlyPlaying {
                            // 播放中的状态
                            Image(systemName: "pause.circle.fill")
                                .font(.title2)
                                .foregroundColor(themeManager.accentColor)
                                .background(
                                    Circle()
                                        .fill(themeManager.accentColor.opacity(0.2))
                                        .scaleEffect(1.3)
                                        .overlay(
                                            Circle()
                                                .stroke(themeManager.accentColor.opacity(0.6), lineWidth: 2)
                                                .scaleEffect(1.4)
                                                .opacity(0.8)
                                                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isCurrentlyPlaying)
                                        )
                                )
                        } else {
                            // 未播放状态
                            Image(systemName: "play.circle.fill")
                                .font(.title2)
                                .themedAccent()
                        }
                    }
                }

                Button(action: onDelete) {
                    Image(systemName: "trash.circle")
                        .font(.title3)
                        .foregroundColor(.red.opacity(0.7))
                }
            }
        }
        .padding(16)
        .glassmorphism(cornerRadius: 16)
        .overlay(
            // VIP 锁定遮罩
            VIPLockOverlay(isLocked: isLocked, size: .large) {
                onVIPTap?()
            }
        )
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    FavoritesView()
        .background(Color.blue.gradient)
}

import SwiftUI

struct TimerPickerView: View {
    @StateObject private var timerManager = TimerManager.shared
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var selectedHours = 0
    @State private var selectedMinutes = 30
    @State private var showSubscriptionPrompt = false

    let hourOptions = Array(0...8) // 0-8 hours
    let minuteOptions = Array(0...59) // 0-59 minutes
    
    var body: some View {
        NavigationView {
            ZStack {
                AnimatedGradientBackground()

                VStack(spacing: 0) {
                    // Header with close button
                    headerWithCloseButton

                    ScrollView {
                        VStack(spacing: 24) {
                            // Title and description
                            titleSection

                            // Quick Timer Options
                            quickTimerSection

                            // Custom Timer Picker
                            customTimerSection

                            // Action Buttons
                            actionButtons
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showSubscriptionPrompt) {
            SubscriptionPromptView(feature: .longTimer)
        }
    }
    
    private var headerWithCloseButton: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .themedText()
                    .frame(width: 44, height: 44)
                    .glassmorphism(cornerRadius: 12)
            }

            Spacer()

            Text(L10nManager.setTimer)
                .font(.title)
                .fontWeight(.bold)
                .themedText()
                .multilineTextAlignment(.center)

            Spacer()

            Color.clear
                .frame(width: 44, height: 44)
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }

    private var titleSection: some View {
        VStack(spacing: 12) {
            Text(L10nManager.timerDescription)
                .font(.body)
                .themedText()
                .opacity(0.8)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .padding(.horizontal, 10)
        }
    }
    
    private var quickTimerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(L10nManager.quickSelect)
                .font(.headline)
                .themedText()

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(quickTimerOptions, id: \.minutes) { option in
                    QuickTimerButton(
                        title: option.title,
                        minutes: option.minutes,
                        isSelected: selectedHours == 0 && selectedMinutes == option.minutes
                    ) {
                        selectedHours = 0
                        selectedMinutes = option.minutes
                    }
                }
            }
        }
        .padding(20)
        .glassmorphism(cornerRadius: 16)
    }
    
    private var customTimerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(L10nManager.customTime)
                .font(.headline)
                .themedText()

            HStack(spacing: 20) {
                // Hours Picker
                VStack(spacing: 8) {
                    Text(L10nManager.hours)
                        .font(.caption)
                        .themedText()
                        .opacity(0.7)

                    Picker("Hours", selection: $selectedHours) {
                        ForEach(hourOptions, id: \.self) { hour in
                            Text("\(hour)")
                                .tag(hour)
                                .fontWeight(.medium)
                                .themedText()
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: 80, height: 120)
                    .clipped()
                }

                Text(":")
                    .font(.title)
                    .themedText()
                    .padding(.top, 20)

                // Minutes Picker
                VStack(spacing: 8) {
                    Text(L10nManager.minutes)
                        .font(.caption)
                        .themedText()
                        .opacity(0.7)

                    Picker("Minutes", selection: $selectedMinutes) {
                        ForEach(minuteOptions, id: \.self) { minute in
                            Text(String(format: "%02d", minute))
                                .tag(minute)
                                .fontWeight(.medium)
                                .themedText()
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: 80, height: 120)
                    .clipped()
                }
            }
            .frame(maxWidth: .infinity)
        }
        .padding(20)
        .glassmorphism(cornerRadius: 16)
    }
    
    private var actionButtons: some View {
        VStack(spacing: 16) {
            // Start Timer Button
            Button(action: startTimer) {
                HStack {
                    Image(systemName: "timer")
                    Text(L10nManager.startTimer)
                }
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(height: 56)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 28)
                        .fill(themeManager.accentColor)
                )
                .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
            }
            .disabled(selectedHours == 0 && selectedMinutes == 0)
            .opacity((selectedHours == 0 && selectedMinutes == 0) ? 0.5 : 1.0)

            // Cancel Button
            Button(action: { dismiss() }) {
                Text(L10nManager.cancel)
                    .font(.system(size: 16, weight: .medium))
                    .themedText()
                    .opacity(0.7)
            }
        }
    }
    
    private func startTimer() {
        let totalSeconds = TimeInterval(selectedHours * 3600 + selectedMinutes * 60)

        // 检查是否超过 30 分钟且用户未订阅 VIP
        if totalSeconds > 30 * 60 && !subscriptionManager.isSubscribed {
            showSubscriptionPrompt = true
            return
        }

        // 正常启动定时器
        timerManager.startTimer(duration: totalSeconds)
        dismiss()
    }
    
    private var quickTimerOptions: [(title: String, minutes: Int)] {
        [
            (L10nManager.timer15min, 15),
            (L10nManager.timer30min, 30),
            (L10nManager.timer45min, 45),
            (L10nManager.timer1hour, 60),
            (L10nManager.timer1_5hour, 90),
            (L10nManager.timer2hour, 120)
        ]
    }
}

struct QuickTimerButton: View {
    let title: String
    let minutes: Int
    let isSelected: Bool
    let action: () -> Void
    
    @StateObject private var themeManager = ThemeManager.shared
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .themedText()
                
                Text("\(minutes)min")
                    .font(.caption2)
                    .themedText()
                    .opacity(0.7)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? themeManager.accentColor.opacity(0.3) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isSelected ? themeManager.accentColor : Color.white.opacity(0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Timer Picker Sheet Extension
extension PlayControllerView {
    var timerPickerSheet: some View {
        TimerPickerView()
    }
}

#Preview {
    TimerPickerView()
}

import SwiftUI

struct SoundDetailView: View {
    let category: SoundCategory
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            // Background
            AnimatedGradientBackground()
            
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Sounds List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(category.sounds) { sound in
                            SoundItemRow(sound: sound)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 20) // 底部间距
                }
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar(.hidden, for: .navigationBar)
    }
    
    private var headerView: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .themedText()
                        .frame(width: 44, height: 44)
                        .glassmorphism(cornerRadius: 12)
                }
                
                Spacer()
                
                VStack {
                    Image(systemName: category.icon)
                        .font(.title)
                        .themedAccent()
                    
                    Text(category.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .themedText()
                }
                
                Spacer()
                
                // Placeholder for symmetry
                Color.clear
                    .frame(width: 44, height: 44)
            }
            
            Text(category.description)
                .font(.subheadline)
                .themedText()
                .opacity(0.8)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
    

}

struct SoundItemRow: View {
    let sound: SoundItem
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared

    @State private var showVIPSoundPrompt = false
    @State private var showMixLimitPrompt = false

    private var isPlaying: Bool {
        audioManager.activeSounds[sound.id]?.isPlaying ?? false
    }

    private var currentVolume: Float {
        // 监听 volumeUpdateTrigger 来确保UI更新
        _ = audioManager.volumeUpdateTrigger
        return audioManager.activeSounds[sound.id]?.volume ?? sound.volume
    }

    private var isVIPLocked: Bool {
        return sound.isVIP && !subscriptionManager.canUseVIPSound()
    }
    
    var body: some View {


        VStack(spacing: 12) {
            HStack {
                // Play/Pause Button
                Button(action: togglePlayback) {
                    Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                        .font(.title2)
                        .themedAccent()
                        .frame(width: 44, height: 44)
                }
                .disabled(isVIPLocked) // VIP 锁定时禁用播放按钮
                .opacity(isVIPLocked ? 0.5 : 1.0)
                
                // Sound Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(sound.name)
                            .font(.headline)
                            .themedText()

                        if sound.isVIP {
                            VIPBadge(size: .small, style: .gold)
                        }

                        Spacer()
                    }

                    Text(sound.description)
                        .font(.caption)
                        .themedText()
                        .opacity(0.7)
                        .lineLimit(2)
                }
                
                Spacer()
            }
            
            // Volume Control (only show when playing)
            if isPlaying {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "speaker.fill")
                            .font(.caption)
                            .themedAccent()
                        
                        Slider(value: Binding(
                            get: { currentVolume },
                            set: { newValue in
                                audioManager.setVolume(for: sound.id, volume: newValue)
                            }
                        ), in: 0...1)
                        .accentColor(themeManager.accentColor)

                        Text("\(Int(currentVolume * 100))%")
                            .font(.caption)
                            .themedAccent()
                            .frame(width: 35)
                    }
                }
                .transition(.opacity.combined(with: .scale))
            }
        }
        .padding(16)
        .glassmorphism(cornerRadius: 16)
        .overlay(
            VIPLockOverlay(isLocked: isVIPLocked, size: .medium) {
                // VIP 锁定遮罩被点击，显示 VIP 声音订阅提示

                showVIPSoundPrompt = true
            }
        )
        .animation(.easeInOut(duration: 0.3), value: isPlaying)
        .sheet(isPresented: $showVIPSoundPrompt) {

            SubscriptionPromptView(feature: .vipSound)
        }
        .sheet(isPresented: $showMixLimitPrompt) {

            SubscriptionPromptView(feature: .mixLimit)
        }
    }

    private func togglePlayback() {
        if isPlaying {
            // 从声音列表停止声音时，应该从混音器中完全移除
            audioManager.stopSound(sound.id)
        } else {
            // VIP 权限检查现在在 VIPLockOverlay 中处理
            // 这里只检查混音数量限制


            // 检查混音数量限制（只有在添加新声音时检查）
            let isNewSound = audioManager.activeSounds[sound.id] == nil
            let currentCount = audioManager.activeSounds.count
            let canAddMore = subscriptionManager.canAddMoreSoundsToMix(currentCount: currentCount)


            if isNewSound && !canAddMore {

                showMixLimitPrompt = true
                return
            }

            // 权限检查通过，播放声音
            var soundCopy = sound
            soundCopy.volume = currentVolume
            let success = audioManager.playSound(soundCopy)

            if success {

            } else {

            }
        }
    }


}

#Preview {
    SoundDetailView(category: SoundCategory.sampleCategories[0])
}

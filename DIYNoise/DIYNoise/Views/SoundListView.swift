import SwiftUI

struct SoundListView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var languageManager = LanguageManager.shared
    @State private var selectedCategory: SoundCategory?
    @State private var refreshTrigger = UUID()
    
    let categories = SoundCategory.sampleCategories
    
    var body: some View {
        ZStack {
            // Background - ensure it's visible even when MainTabView background might not show through
            AnimatedGradientBackground()

            VStack(spacing: 0) {
                // Header
                headerView

                // Categories Grid
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(categories) { category in
                            CategoryCard(category: category) {
                                selectedCategory = category
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 120) // 为Tab栏和播放控制器留出更多空间
                }
            }
        }
        .sheet(item: $selectedCategory) { category in
            SoundDetailView(category: category)
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger = UUID()
        }
        .id(refreshTrigger)
    }
    
    private var headerView: some View {
        VStack(spacing: 8) {
            VStack(alignment: .leading, spacing: 4) {
                Text(L10nManager.appName)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .themedText()

                Text(L10nManager.appSubtitle)
                    .font(.subheadline)
                    .themedText()
                    .opacity(0.8)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
        }
    }
}

#Preview {
    SoundListView()
        .background(Color.blue.gradient)
}

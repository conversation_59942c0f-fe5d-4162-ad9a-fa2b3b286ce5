import SwiftUI

struct ThemeSelectorView: View {
    @ObservedObject private var themeManager = ThemeManager.shared
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var showSubscriptionPrompt = false
    @State private var selectedTheme: AppTheme?
    @State private var showThemeConfirmation = false
    
    var body: some View {
        ZStack {
            AnimatedGradientBackground()
            
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Theme Grid
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(Array(themeManager.availableThemes.enumerated()), id: \.element.id) { index, theme in
                            ThemeCard(
                                theme: theme,
                                isSelected: theme.id == themeManager.currentTheme.id,
                                isLocked: theme.isVIP && !subscriptionManager.canUseTheme(themeIndex: index)
                            ) {
                                // 主题卡片点击事件 - 显示确认对话框
                                if theme.isVIP && !subscriptionManager.canUseTheme(themeIndex: index) {
                                    // VIP 主题但无权限，不执行任何操作（由 VIP 遮罩处理）
                                } else if theme.id != themeManager.currentTheme.id {
                                    // 只有选择不同主题时才显示确认
                                    selectedTheme = theme
                                    showThemeConfirmation = true
                                }
                            } onVIPTap: {
                                // VIP 锁定遮罩点击事件
                                showSubscriptionPrompt = true
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
                
                // Bottom Controls
                // bottomControlsView
            }
        }
        .onAppear {
            // 确保主题状态正确同步


            // 强制刷新 UI 状态
            DispatchQueue.main.async {
                // 触发 UI 更新
                themeManager.objectWillChange.send()
            }
        }
        .sheet(isPresented: $showSubscriptionPrompt) {
            SubscriptionPromptView(feature: .vipTheme)
        }
        .alert(L10nManager.confirmThemeChange, isPresented: $showThemeConfirmation) {
            Button(L10nManager.cancel, role: .cancel) {
                selectedTheme = nil
            }
            Button(L10nManager.confirm) {
                if let theme = selectedTheme {
                    themeManager.setTheme(theme)
                }
                selectedTheme = nil
            }
        } message: {
            if let theme = selectedTheme {
                Text(L10nManager.confirmThemeChangeMessage(theme.name))
            }
        }
    }
    
    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .themedText()
                    .frame(width: 44, height: 44)
                    .glassmorphism(cornerRadius: 12)
            }
            
            Spacer()
            
            VStack {
                Text(L10nManager.themeColor)
                    .font(.title2)
                    .fontWeight(.bold)
                    .themedText()

                Text(L10nManager.currentTheme(themeManager.currentTheme.name))
                    .font(.caption)
                    .themedText()
                    .opacity(0.7)
            }
            
            Spacer()
            
            Color.clear
                .frame(width: 44, height: 44)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
    
    private var bottomControlsView: some View {
        HStack(spacing: 16) {
            Button(action: { themeManager.previousTheme() }) {
                HStack {
                    Image(systemName: "chevron.left")
                    Text(L10nManager.previousTheme)
                }
                .font(.system(size: 16, weight: .medium))
                .themedText()
                .frame(height: 50)
                .frame(maxWidth: .infinity)
                .glassmorphism(cornerRadius: 25)
            }

            Button(action: { themeManager.nextTheme() }) {
                HStack {
                    Text(L10nManager.nextTheme)
                    Image(systemName: "chevron.right")
                }
                .font(.system(size: 16, weight: .medium))
                .themedText()
                .frame(height: 50)
                .frame(maxWidth: .infinity)
                .glassmorphism(cornerRadius: 25)
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
}

struct ThemeCard: View {
    let theme: AppTheme
    let isSelected: Bool
    let isLocked: Bool
    let action: () -> Void
    let onVIPTap: (() -> Void)?

    @State private var isPressed = false

    init(theme: AppTheme, isSelected: Bool, isLocked: Bool = false, action: @escaping () -> Void, onVIPTap: (() -> Void)? = nil) {
        self.theme = theme
        self.isSelected = isSelected
        self.isLocked = isLocked
        self.action = action
        self.onVIPTap = onVIPTap
    }
    
    var body: some View {
        VStack(spacing: 12) {
            // Theme Preview
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(theme.primaryGradient.swiftUIGradient)
                    .frame(height: 80)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(theme.secondaryGradient.swiftUIGradient)
                            .opacity(0.3)
                    )

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .frame(width: 40, height: 40)
                        )
                }
            }

            // Theme Name
            HStack {
                Text(theme.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)  // 统一使用白色文字
                    .shadow(color: Color.black.opacity(0.8), radius: 2, x: 0, y: 1)  // 增强阴影效果

                if theme.isVIP {
                    VIPBadge(size: .small, style: .crown)
                }
            }

            // Color Indicators
            HStack(spacing: 4) {
                ForEach(theme.primaryGradient.colors, id: \.self) { colorHex in
                    Circle()
                        .fill(Color(hex: colorHex))
                        .frame(width: 12, height: 12)
                }
            }

            // 选择按钮 - 只有这个区域可点击
            Button(action: action) {
                HStack {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .font(.system(size: 16, weight: .medium))
                    Text(isSelected ? L10nManager.themeInUse : L10nManager.selectTheme)
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(isSelected ? theme.accentColor.color : .white)  // 未选中时使用白色文字
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? theme.accentColor.color.opacity(0.2) : Color.white.opacity(0.1))  // 增强背景对比度
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(isSelected ? theme.accentColor.color : Color.white.opacity(0.3), lineWidth: 1)  // 未选中时使用白色边框
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
        }
        .padding(16)
        .frame(maxWidth: .infinity)
        .frame(height: 200) // 增加高度以容纳按钮
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.black.opacity(0.3))  // 使用深色半透明背景确保文字可读性
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.ultraThinMaterial)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            isSelected ? theme.accentColor.color : Color.clear,
                            lineWidth: 3
                        )
                )
        )
        .overlay(
            VIPLockOverlay(isLocked: isLocked, size: .large) {
                // VIP 主题锁定遮罩被点击

                onVIPTap?()
            }
        )
    }


}

#Preview {
    ThemeSelectorView()
}

import SwiftUI

struct PlayControllerView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var timerManager = TimerManager.shared
    @Binding var showMixer: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            // Timer and Controls Row
            HStack(spacing: 16) {
                // Timer Display
                timerSection
                
                Spacer()
                
                // Play Controls
                playControlsSection
                
                Spacer()
                
                // Mixer Button
                mixerButton
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            
            // Progress Bar (if timer is active)
            if timerManager.isTimerActive {
                progressBar
            }
        }
        .glassmorphism(cornerRadius: 16)
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
        .sheet(isPresented: $timerManager.showTimerPicker) {
            TimerPickerView()
        }
    }
    
    private var timerSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            if timerManager.isTimerActive {
                Text(L10nManager.timeRemaining)
                    .font(.caption2)
                    .themedText()
                    .opacity(0.7)

                Text(timerManager.formattedTimeRemaining)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .themedText()
                    .monospacedDigit()
            } else {
                Button(action: {
                    timerManager.showTimerPicker = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "timer")
                            .font(.caption)
                        Text(L10nManager.timer)
                            .font(.caption)
                    }
                    .themedAccent()
                }
            }
        }
        .frame(minWidth: 80, alignment: .leading)
    }
    
    private var playControlsSection: some View {
        HStack(spacing: 12) {
            // Play/Pause All
            Button(action: togglePlayback) {
                Image(systemName: audioManager.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                    .font(.title)
                    .themedAccent()
            }
            
            // Stop All
            if audioManager.isPlaying {
                Button(action: { audioManager.stopAllSounds() }) {
                    Image(systemName: "stop.circle")
                        .font(.title2)
                        .foregroundColor(.red.opacity(0.8))
                }
            }
        }
    }
    
    private var mixerButton: some View {
        Button(action: { showMixer = true }) {
            VStack(spacing: 2) {
                Image(systemName: "slider.horizontal.3")
                    .font(.title3)
                Text(L10nManager.mixerTitle)
                    .font(.caption2)
            }
            .themedAccent()
        }
        .frame(minWidth: 60)
    }
    
    private var progressBar: some View {
        VStack(spacing: 4) {
            ProgressView(value: timerManager.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: themeManager.accentColor))
                .scaleEffect(y: 0.8)
            
            HStack {
                Text(L10nManager.elapsedTime(timerManager.formattedElapsedTime))
                    .font(.caption2)
                    .themedText()
                    .opacity(0.6)

                Spacer()

                Button(action: { timerManager.stopTimer() }) {
                    Text(L10nManager.cancelTimer)
                        .font(.caption2)
                        .themedAccent()
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 8)
    }
    
    private func togglePlayback() {
        if audioManager.isPlaying {
            audioManager.pauseAllSounds()
        } else {
            audioManager.resumeAllSounds()
        }
    }
}

// MARK: - Timer Manager
class TimerManager: ObservableObject {
    static let shared = TimerManager()

    @Published var isTimerActive = false
    @Published var timeRemaining: TimeInterval = 0
    @Published var totalTime: TimeInterval = 0
    @Published var showTimerPicker = false
    
    private var timer: Timer?
    
    var progress: Double {
        guard totalTime > 0 else { return 0 }
        return (totalTime - timeRemaining) / totalTime
    }
    
    var formattedTimeRemaining: String {
        formatTime(timeRemaining)
    }
    
    var formattedElapsedTime: String {
        formatTime(totalTime - timeRemaining)
    }
    
    func startTimer(duration: TimeInterval) {
        stopTimer()

        totalTime = duration
        timeRemaining = duration
        isTimerActive = true

        // 通知 AudioManager 更新 Now Playing 信息
        AudioManager.shared.updateNowPlayingInfo()

        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.timeRemaining -= 1

            // 每分钟更新一次 Now Playing 信息
            if Int(self.timeRemaining) % 60 == 0 {
                AudioManager.shared.updateNowPlayingInfo()
            }

            if self.timeRemaining <= 0 {
                self.timerFinished()
            }
        }
    }
    
    func stopTimer() {
        timer?.invalidate()
        timer = nil
        isTimerActive = false
        timeRemaining = 0
        totalTime = 0

        // 通知 AudioManager 更新 Now Playing 信息（移除定时器信息）
        AudioManager.shared.updateNowPlayingInfo()
    }
    
    private func timerFinished() {
        stopTimer()
        AudioManager.shared.stopAllSounds()

        // 可以添加通知或其他完成动作
        DispatchQueue.main.async {
            // 播放完成提示音或显示通知
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
}

#Preview {
    PlayControllerView(showMixer: .constant(false))
        .padding()
        .background(Color.blue.gradient)
}

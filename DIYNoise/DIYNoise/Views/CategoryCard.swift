import SwiftUI

struct CategoryCard: View {
    let category: SoundCategory
    let action: () -> Void

    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @State private var isPressed = false
    @State private var pulseAnimation = false

    // 检查该分类下是否有声音在播放
    private var hasPlayingSounds: Bool {
        // 监听音量更新触发器以确保实时更新
        _ = audioManager.volumeUpdateTrigger

        let categoryPlayingSounds = category.sounds.filter { sound in
            audioManager.activeSounds[sound.id]?.isPlaying ?? false
        }
        return !categoryPlayingSounds.isEmpty
    }

    // 获取播放中的声音数量
    private var playingSoundCount: Int {
        category.sounds.filter { sound in
            audioManager.activeSounds[sound.id]?.isPlaying ?? false
        }.count
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Icon with playing indicator
                ZStack {
                    Circle()
                        .fill(themeManager.accentColor.opacity(hasPlayingSounds ? 0.3 : 0.2))
                        .frame(width: 60, height: 60)
                        .overlay(
                            // 播放状态的脉冲动画边框
                            Group {
                                if hasPlayingSounds {
                                    Circle()
                                        .stroke(themeManager.accentColor, lineWidth: 2)
                                        .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                                        .opacity(pulseAnimation ? 0.3 : 0.8)
                                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: pulseAnimation)
                                }
                            }
                        )

                    Image(systemName: category.icon)
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(themeManager.accentColor)

                    // 播放指示器
                    if hasPlayingSounds {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                ZStack {
                                    Circle()
                                        .fill(themeManager.accentColor)
                                        .frame(width: 20, height: 20)

                                    Image(systemName: "waveform")
                                        .font(.system(size: 8, weight: .bold))
                                        .foregroundColor(.white)
                                        .scaleEffect(pulseAnimation ? 1.3 : 1.0)
                                        .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: pulseAnimation)
                                }
                                .offset(x: 8, y: 8)
                            }
                        }
                    }
                }
                
                // Title
                Text(category.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .themedText()
                
                // Description
                Text(category.description)
                    .font(.caption)
                    .themedText()
                    .opacity(0.7)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                
                // Sound count with playing status
                HStack(spacing: 4) {
                    if hasPlayingSounds {
                        Text(L10nManager.playingCount(playingSoundCount, category.sounds.count))
                            .font(.caption2)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(themeManager.accentColor)
                            )
                    } else {
                        Text(L10nManager.soundCount(category.sounds.count))
                            .font(.caption2)
                            .themedAccent()
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(themeManager.accentColor.opacity(0.2))
                            )
                    }
                }
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .frame(height: 180)
            .glassmorphism(cornerRadius: 20)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .onChange(of: hasPlayingSounds) { _, newValue in
            if newValue {
                // 开始呼吸动效
                pulseAnimation = true
            } else {
                // 停止呼吸动效
                pulseAnimation = false
            }
        }
        .onAppear {
            // 如果已经有声音在播放，立即开始动画
            if hasPlayingSounds {
                pulseAnimation = true
            }
        }
    }
}

#Preview {
    CategoryCard(category: SoundCategory.sampleCategories[0]) {

    }
    .padding()
    .background(Color.blue.gradient)
}

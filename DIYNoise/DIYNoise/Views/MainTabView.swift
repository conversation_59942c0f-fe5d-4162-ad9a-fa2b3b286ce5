import SwiftUI

struct MainTabView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @State private var selectedTab = 0
    @State private var showMixer = false
    @State private var showFloatingController = false
    
    var body: some View {
        ZStack {
            // Background
            AnimatedGradientBackground()

            // Tab Content
            TabView(selection: $selectedTab) {
                // 声音列表页面
                SoundListView()
                    .tabItem {
                        Image(systemName: "waveform")
                        Text(L10nManager.tabSounds)
                    }
                    .tag(0)

                // 收藏页面
                FavoritesView()
                    .tabItem {
                        Image(systemName: "heart.fill")
                        Text(L10nManager.tabFavorites)
                    }
                    .tag(1)

                // 设置页面
                SettingsView()
                    .tabItem {
                        Image(systemName: "gearshape.fill")
                        Text(L10nManager.tabSettings)
                    }
                    .tag(2)
            }
            .accentColor(themeManager.accentColor)
            .onAppear {
                setupTabBarAppearance()
            }
            .onChange(of: themeManager.currentTheme.id) {
                setupTabBarAppearance()
            }

            // 悬浮播放控制器 - 一直显示
            FloatingPlayController(
                showMixer: $showMixer,
                isExpanded: $showFloatingController
            )
        }
        .sheet(isPresented: $showMixer) {
            MixerView()
        }
        .animation(.easeInOut(duration: 0.3), value: audioManager.isPlaying)
    }

    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()

        // 设置半透明背景 - 既能看到背景渐变，又有足够对比度
        appearance.configureWithTransparentBackground()

        // 设置半透明的背景色，让渐变透过来但保持按钮可见性
        appearance.backgroundColor = UIColor.black.withAlphaComponent(0.3)

        // 使用毛玻璃效果增加质感和可读性
        appearance.backgroundEffect = UIBlurEffect(style: .systemUltraThinMaterial)

        // 设置选中和未选中的图标颜色
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor(themeManager.accentColor)
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor(themeManager.accentColor)
        ]

        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.white.withAlphaComponent(0.8)
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.white.withAlphaComponent(0.8)
        ]

        // 应用外观设置
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }

}

// MARK: - 悬浮播放控制器
struct FloatingPlayController: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var timerManager = TimerManager.shared
    @Binding var showMixer: Bool
    @Binding var isExpanded: Bool

    var body: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()

                if isExpanded {
                    // 展开的控制器
                    expandedController
                        .transition(.asymmetric(
                            insertion: .move(edge: .trailing).combined(with: .opacity),
                            removal: .move(edge: .trailing).combined(with: .opacity)
                        ))
                } else {
                    // 收起的悬浮按钮
                    floatingButton
                        .transition(.asymmetric(
                            insertion: .scale.combined(with: .opacity),
                            removal: .scale.combined(with: .opacity)
                        ))
                }
            }
            .padding(.trailing, 20)
            .padding(.bottom, 100) // 避免与Tab栏重叠
        }
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isExpanded)
        .sheet(isPresented: $timerManager.showTimerPicker) {
            TimerPickerView()
        }
    }

    private var floatingButton: some View {
        Button(action: {
            withAnimation {
                isExpanded.toggle()
            }
        }) {
            ZStack {
                Circle()
                    .fill(themeManager.accentColor)
                    .frame(width: 56, height: 56)
                    .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)

                Image(systemName: "slider.horizontal.3")
                    .font(.title2)
                    .foregroundColor(.white)
            }
        }
    }

    private var expandedController: some View {
        VStack(spacing: 12) {
            // 控制按钮行
            HStack(spacing: 16) {
                // 收起按钮
                Button(action: {
                    withAnimation {
                        isExpanded = false
                    }
                }) {
                    Image(systemName: "chevron.right")
                        .font(.title3)
                        .foregroundColor(.white.opacity(0.8))
                }

                // 播放/暂停
                Button(action: togglePlayback) {
                    Image(systemName: audioManager.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                        .font(.title)
                        .themedAccent()
                }

                // 停止
                if audioManager.isPlaying {
                    Button(action: { audioManager.stopAllSounds() }) {
                        Image(systemName: "stop.circle")
                            .font(.title2)
                            .foregroundColor(.red.opacity(0.8))
                    }
                }

                // 混音器
                Button(action: { showMixer = true }) {
                    Image(systemName: "slider.horizontal.3")
                        .font(.title2)
                        .themedAccent()
                }

                // 定时器
                Button(action: {
                    timerManager.showTimerPicker = true
                }) {
                    Image(systemName: timerManager.isTimerActive ? "timer" : "timer.circle")
                        .font(.title2)
                        .themedAccent()
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)

            // 定时器进度条（如果激活）
            if timerManager.isTimerActive {
                VStack(spacing: 4) {
                    ProgressView(value: timerManager.progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: themeManager.accentColor))
                        .scaleEffect(y: 0.8)

                    Text("\(L10nManager.timeRemaining) \(timerManager.formattedTimeRemaining)")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 8)
            }
        }
        .glassmorphism(cornerRadius: 16)
        .frame(maxWidth: 280)
    }

    private func togglePlayback() {
        if audioManager.isPlaying {
            audioManager.pauseAllSounds()
        } else {
            audioManager.resumeAllSounds()
        }
    }
}

#Preview {
    MainTabView()
}

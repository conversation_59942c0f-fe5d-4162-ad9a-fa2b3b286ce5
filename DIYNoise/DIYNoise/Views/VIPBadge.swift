import SwiftUI

// MARK: - VIP Badge Component
struct VIPBadge: View {
    let size: VIPBadgeSize
    let style: VIPBadgeStyle
    
    enum VIPBadgeSize {
        case small
        case medium
        case large
        
        var fontSize: CGFloat {
            switch self {
            case .small: return 8
            case .medium: return 10
            case .large: return 12
            }
        }
        
        var padding: EdgeInsets {
            switch self {
            case .small: return EdgeInsets(top: 2, leading: 4, bottom: 2, trailing: 4)
            case .medium: return EdgeInsets(top: 3, leading: 6, bottom: 3, trailing: 6)
            case .large: return EdgeInsets(top: 4, leading: 8, bottom: 4, trailing: 8)
            }
        }
        
        var cornerRadius: CGFloat {
            switch self {
            case .small: return 6
            case .medium: return 8
            case .large: return 10
            }
        }
    }
    
    enum VIPBadgeStyle {
        case gold
        case premium
        case crown
        
        var backgroundColor: Color {
            switch self {
            case .gold: return Color(red: 1.0, green: 0.84, blue: 0.0) // 金色
            case .premium: return Color(red: 0.6, green: 0.4, blue: 0.8) // 紫色
            case .crown: return Color(red: 1.0, green: 0.65, blue: 0.0) // 橙金色
            }
        }
        
        var textColor: Color {
            switch self {
            case .gold: return Color.black
            case .premium: return Color.white
            case .crown: return Color.white
            }
        }
        
        var icon: String? {
            switch self {
            case .gold: return nil
            case .premium: return "star.fill"
            case .crown: return "crown.fill"
            }
        }
        
        var text: String {
            switch self {
            case .gold: return "VIP"
            case .premium: return "VIP"
            case .crown: return "VIP"
            }
        }
    }
    
    init(size: VIPBadgeSize = .medium, style: VIPBadgeStyle = .gold) {
        self.size = size
        self.style = style
    }
    
    var body: some View {
        HStack(spacing: 2) {
            if let icon = style.icon {
                Image(systemName: icon)
                    .font(.system(size: size.fontSize - 1, weight: .bold))
                    .foregroundColor(style.textColor)
            }
            
            Text(style.text)
                .font(.system(size: size.fontSize, weight: .bold))
                .foregroundColor(style.textColor)
        }
        .padding(size.padding)
        .background(
            RoundedRectangle(cornerRadius: size.cornerRadius)
                .fill(style.backgroundColor)
                .shadow(color: .black.opacity(0.2), radius: 1, x: 0, y: 1)
        )
    }
}

// MARK: - VIP Lock Overlay
struct VIPLockOverlay: View {
    let isLocked: Bool
    let size: VIPBadge.VIPBadgeSize
    let onTap: (() -> Void)?

    init(isLocked: Bool, size: VIPBadge.VIPBadgeSize = .medium, onTap: (() -> Void)? = nil) {
        self.isLocked = isLocked
        self.size = size
        self.onTap = onTap
    }

    var body: some View {
        if isLocked {
            ZStack {
                // 半透明遮罩
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.6))

                VStack(spacing: 4) {
                    Image(systemName: "lock.fill")
                        .font(.system(size: size == .large ? 20 : 16, weight: .medium))
                        .foregroundColor(.white)

                    VIPBadge(size: size, style: .crown)
                }
            }
            .contentShape(Rectangle())
            .onTapGesture {

                onTap?()
            }
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        HStack(spacing: 10) {
            VIPBadge(size: .small, style: .gold)
            VIPBadge(size: .medium, style: .gold)
            VIPBadge(size: .large, style: .gold)
        }
        
        HStack(spacing: 10) {
            VIPBadge(size: .small, style: .premium)
            VIPBadge(size: .medium, style: .premium)
            VIPBadge(size: .large, style: .premium)
        }
        
        HStack(spacing: 10) {
            VIPBadge(size: .small, style: .crown)
            VIPBadge(size: .medium, style: .crown)
            VIPBadge(size: .large, style: .crown)
        }
        
        // VIP Lock Overlay 示例
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.blue.opacity(0.3))
                .frame(width: 120, height: 80)
            
            VIPLockOverlay(isLocked: true, size: .medium)
        }
    }
    .padding()
}

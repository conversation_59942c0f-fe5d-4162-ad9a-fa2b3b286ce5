import SwiftUI

// MARK: - App Theme
struct AppTheme: Identifiable, Codable {
    let id: String // 使用固定的字符串标识符而不是随机 UUID
    let nameKey: String // 本地化键值
    let primaryGradient: GradientColors
    let secondaryGradient: GradientColors
    let accentColor: ColorHex
    let textColor: ColorHex
    let cardBackgroundColor: ColorHex
    let isVIP: Bool // VIP 标签

    // 本地化名称
    var name: String {
        switch nameKey {
        case "ocean_breeze": return L10nManager.themeOceanBreeze
        case "sunset_glow": return L10nManager.themeSunsetGlow
        case "forest_mist": return L10nManager.themeForestMist
        case "night_sky": return L10nManager.themeNightSky
        case "lavender_dream": return L10nManager.themeLavenderDream
        case "fire_ember": return L10nManager.themeFireEmber
        default: return nameKey
        }
    }
    
    static let defaultThemes: [AppTheme] = [
        // 海洋主题
        AppTheme(
            id: "ocean",
            nameKey: "ocean_breeze",
            primaryGradient: GradientColors(
                colors: ["#667eea", "#764ba2"],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ),
            secondaryGradient: GradientColors(
                colors: ["#f093fb", "#f5576c"],
                startPoint: .top,
                endPoint: .bottom
            ),
            accentColor: ColorHex("#667eea"),
            textColor: ColorHex("#ffffff"),
            cardBackgroundColor: ColorHex("#ffffff20"),
            isVIP: false
        ),
        
        // 森林主题
        AppTheme(
            id: "forest",
            nameKey: "forest_mist",
            primaryGradient: GradientColors(
                colors: ["#11998e", "#38ef7d"],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ),
            secondaryGradient: GradientColors(
                colors: ["#134e5e", "#71b280"],
                startPoint: .top,
                endPoint: .bottom
            ),
            accentColor: ColorHex("#11998e"),
            textColor: ColorHex("#ffffff"),
            cardBackgroundColor: ColorHex("#ffffff25"),
            isVIP: false
        ),

        // 日落主题
        AppTheme(
            id: "sunset",
            nameKey: "sunset_glow",
            primaryGradient: GradientColors(
                colors: ["#fa709a", "#fee140"],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ),
            secondaryGradient: GradientColors(
                colors: ["#ff9a9e", "#fecfef"],
                startPoint: .top,
                endPoint: .bottom
            ),
            accentColor: ColorHex("#fa709a"),
            textColor: ColorHex("#ffffff"),
            cardBackgroundColor: ColorHex("#ffffff30"),
            isVIP: false
        ),
        
        // 夜空主题 (VIP)
        AppTheme(
            id: "night_sky",
            nameKey: "night_sky",
            primaryGradient: GradientColors(
                colors: ["#2c3e50", "#4a6741"],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ),
            secondaryGradient: GradientColors(
                colors: ["#232526", "#414345"],
                startPoint: .top,
                endPoint: .bottom
            ),
            accentColor: ColorHex("#4a6741"),
            textColor: ColorHex("#ffffff"),
            cardBackgroundColor: ColorHex("#ffffff15"),
            isVIP: true
        ),

        // 薰衣草主题 (VIP)
        AppTheme(
            id: "lavender_dream",
            nameKey: "lavender_dream",
            primaryGradient: GradientColors(
                colors: ["#a8edea", "#fed6e3"],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ),
            secondaryGradient: GradientColors(
                colors: ["#d299c2", "#fef9d7"],
                startPoint: .top,
                endPoint: .bottom
            ),
            accentColor: ColorHex("#a8edea"),
            textColor: ColorHex("#ffffff"),
            cardBackgroundColor: ColorHex("#ffffff35"),
            isVIP: true
        ),

        // 火焰主题 (VIP)
        AppTheme(
            id: "fire_ember",
            nameKey: "fire_ember",
            primaryGradient: GradientColors(
                colors: ["#ff6b6b", "#ffa726"],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ),
            secondaryGradient: GradientColors(
                colors: ["#ff7043", "#ffab40"],
                startPoint: .top,
                endPoint: .bottom
            ),
            accentColor: ColorHex("#ff6b6b"),
            textColor: ColorHex("#ffffff"),
            cardBackgroundColor: ColorHex("#ffffff25"),
            isVIP: true
        )
    ]
}

// MARK: - Gradient Colors
struct GradientColors: Codable {
    let colors: [String]
    let startPoint: GradientPoint
    let endPoint: GradientPoint
    
    var swiftUIGradient: LinearGradient {
        LinearGradient(
            colors: colors.map { Color(hex: $0) },
            startPoint: startPoint.unitPoint,
            endPoint: endPoint.unitPoint
        )
    }
}

// MARK: - Gradient Point
enum GradientPoint: String, Codable, CaseIterable {
    case top = "top"
    case bottom = "bottom"
    case leading = "leading"
    case trailing = "trailing"
    case topLeading = "topLeading"
    case topTrailing = "topTrailing"
    case bottomLeading = "bottomLeading"
    case bottomTrailing = "bottomTrailing"
    case center = "center"
    
    var unitPoint: UnitPoint {
        switch self {
        case .top: return .top
        case .bottom: return .bottom
        case .leading: return .leading
        case .trailing: return .trailing
        case .topLeading: return .topLeading
        case .topTrailing: return .topTrailing
        case .bottomLeading: return .bottomLeading
        case .bottomTrailing: return .bottomTrailing
        case .center: return .center
        }
    }
}

// MARK: - Color Hex
struct ColorHex: Codable {
    let hex: String
    
    init(_ hex: String) {
        self.hex = hex
    }
    
    var color: Color {
        Color(hex: hex)
    }
}

// MARK: - Color Extension
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

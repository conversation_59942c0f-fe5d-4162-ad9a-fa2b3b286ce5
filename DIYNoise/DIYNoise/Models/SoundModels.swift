import Foundation
import SwiftUI

// MARK: - Sound Category
struct SoundCategory: Identifiable, Codable {
    let id = UUID()
    let nameKey: String // 本地化键
    let icon: String
    let sounds: [SoundItem]
    let descriptionKey: String // 本地化键

    // 计算属性，返回本地化的名称和描述
    var name: String {
        return SoundLocalizer.localizedCategoryName(for: nameKey)
    }

    var description: String {
        return SoundLocalizer.localizedCategoryDescription(for: nameKey)
    }

    // 使用本地化键的初始化方法
    init(nameKey: String, icon: String, sounds: [SoundItem], descriptionKey: String? = nil) {
        self.nameKey = nameKey
        self.icon = icon
        self.sounds = sounds
        self.descriptionKey = descriptionKey ?? nameKey
    }
    
    static let sampleCategories: [SoundCategory] = [
        SoundCategory(
            nameKey: "rain",
            icon: "cloud.rain.fill",
            sounds: SoundItem.rainSounds
        ),
        SoundCategory(
            nameKey: "thunder",
            icon: "cloud.bolt.fill",
            sounds: SoundItem.thunderSounds
        ),
        SoundCategory(
            nameKey: "wind",
            icon: "wind",
            sounds: SoundItem.windSounds
        ),
        SoundCategory(
            nameKey: "ocean",
            icon: "water.waves",
            sounds: SoundItem.oceanSounds
        ),
        SoundCategory(
            nameKey: "birds",
            icon: "bird.fill",
            sounds: SoundItem.birdSounds
        ),
        SoundCategory(
            nameKey: "insects",
            icon: "ladybug.fill",
            sounds: SoundItem.insectsSounds
        ),
        SoundCategory(
            nameKey: "fire",
            icon: "flame.fill",
            sounds: SoundItem.fireSounds
        ),
        SoundCategory(
            nameKey: "city",
            icon: "building.2.fill",
            sounds: SoundItem.citySounds
        )
    ]
}

// MARK: - Sound Item
struct SoundItem: Identifiable, Codable {
    let id: UUID
    let nameKey: String // 本地化键
    let fileName: String
    let duration: TimeInterval
    let descriptionKey: String // 本地化键
    let isVIP: Bool // VIP 标签
    var isPlaying: Bool = false
    var volume: Float = 0.5

    // 计算属性，返回本地化的名称和描述
    var name: String {
        return SoundLocalizer.localizedName(for: nameKey)
    }

    var description: String {
        return SoundLocalizer.localizedDescription(for: nameKey)
    }

    // 使用本地化键的初始化方法
    init(nameKey: String, fileName: String, duration: TimeInterval, descriptionKey: String? = nil, isVIP: Bool = false) {
        // 基于 nameKey 生成固定的 UUID，确保每次启动应用时 ID 都相同
        self.id = UUID(uuidString: SoundItem.generateFixedUUID(from: nameKey)) ?? UUID()
        self.nameKey = nameKey
        self.fileName = fileName
        self.duration = duration
        self.descriptionKey = descriptionKey ?? nameKey
        self.isVIP = isVIP
    }

    // 基于字符串生成固定的 UUID
    private static func generateFixedUUID(from string: String) -> String {
        // 使用 MD5 哈希生成固定的 UUID 格式字符串
        let data = string.data(using: .utf8) ?? Data()
        let hash = data.withUnsafeBytes { bytes in
            var hash = [UInt8](repeating: 0, count: 16)
            // 简单的哈希算法，确保相同输入产生相同输出
            for (index, byte) in bytes.enumerated() {
                hash[index % 16] ^= byte
            }
            return hash
        }

        // 将哈希转换为 UUID 格式
        let uuidString = String(format: "%02x%02x%02x%02x-%02x%02x-%02x%02x-%02x%02x-%02x%02x%02x%02x%02x%02x",
                               hash[0], hash[1], hash[2], hash[3],
                               hash[4], hash[5],
                               hash[6], hash[7],
                               hash[8], hash[9],
                               hash[10], hash[11], hash[12], hash[13], hash[14], hash[15])

        return uuidString
    }
    
    // Rain sounds
    static let rainSounds: [SoundItem] = [
        SoundItem(nameKey: "heavy_rain", fileName: "heavy_rain", duration: 250),
        SoundItem(nameKey: "rain_from_eaves", fileName: "rain_from_eaves", duration: 300, isVIP: true),
        SoundItem(nameKey: "light_rain", fileName: "light_rain", duration: 320,isVIP: true)
    ]
    
    // Thunder sounds
    static let thunderSounds: [SoundItem] = [
        SoundItem(nameKey: "distant_thunder", fileName: "distant_thunder", duration: 180),
        SoundItem(nameKey: "thunder_rumbles", fileName: "thunder_rumbles", duration: 120),
        SoundItem(nameKey: "high_altitude_thunder", fileName: "high_altitude_thunder", duration: 360, isVIP: true),
        SoundItem(nameKey: "undulating_thunder", fileName: "undulating_thunder", duration: 200, isVIP: true)
    ]

    // Wind sounds
    static let windSounds: [SoundItem] = [
        SoundItem(nameKey: "blows_leaves", fileName: "blows_leaves", duration: 240, isVIP: true),
        SoundItem(nameKey: "strong_wind", fileName: "strong_wind", duration: 300, isVIP: true),
        SoundItem(nameKey: "winter_cold_wind", fileName: "winter_cold_wind", duration: 280),
        SoundItem(nameKey: "desert_wind", fileName: "desert_wind", duration: 320),
        SoundItem(nameKey: "breeze_rustles", fileName: "breeze_rustles", duration: 320, isVIP: true),
        SoundItem(nameKey: "breeze_leaves", fileName: "breeze_leaves", duration: 320)
    ]

    // Ocean sounds
    static let oceanSounds: [SoundItem] = [
        SoundItem(nameKey: "water_wave", fileName: "water_wave", duration: 350, isVIP: true),
        SoundItem(nameKey: "sea_wave", fileName: "sea_wave", duration: 350),
        SoundItem(nameKey: "waves_on_shore", fileName: "waves_on_shore", duration: 350),
        SoundItem(nameKey: "bubbling_underwater", fileName: "bubbling_underwater", duration: 400, isVIP: true),
        SoundItem(nameKey: "small_stream", fileName: "small_stream", duration: 300, isVIP: true),
    ]
    
    // Bird sounds
    static let birdSounds: [SoundItem] = [
        SoundItem(nameKey: "morning_birds", fileName: "morning_birds", duration: 250),
        SoundItem(nameKey: "forest_birds", fileName: "forest_birds", duration: 300, isVIP: true),
        SoundItem(nameKey: "nightingale", fileName: "nightingale", duration: 180, isVIP: true),
        SoundItem(nameKey: "bird_chirping", fileName: "bird_chirping", duration: 220),
        SoundItem(nameKey: "bird_outside", fileName: "bird_outside", duration: 220, isVIP: true),
        SoundItem(nameKey: "distant_bird", fileName: "distant_bird", duration: 220),
        SoundItem(nameKey: "cuckoo", fileName: "cuckoo", duration: 220, isVIP: true)
    ]

    // Forest sounds
    static let insectsSounds: [SoundItem] = [
        SoundItem(nameKey: "cricket_chirping", fileName: "cricket_chirping", duration: 400),
        SoundItem(nameKey: "summer_evening_frog", fileName: "summer_evening_frog", duration: 300, isVIP: true),
        SoundItem(nameKey: "midsummer_insect_chirping", fileName: "midsummer_insect_chirping", duration: 280),
        SoundItem(nameKey: "frog_sounds", fileName: "frog_sounds", duration: 350),
        SoundItem(nameKey: "cicadas_chirping", fileName: "cicadas_chirping", duration: 300, isVIP: true),
        SoundItem(nameKey: "bees_flying", fileName: "bees_flying", duration: 300)
    ]

    // Fire sounds
    static let fireSounds: [SoundItem] = [
        SoundItem(nameKey: "campfire", fileName: "campfire", duration: 300, isVIP: true),
        SoundItem(nameKey: "fire_crackling", fileName: "fire_crackling", duration: 350),
        SoundItem(nameKey: "flame_sound", fileName: "flame_sound", duration: 280)
    ]

    // City sounds
    static let citySounds: [SoundItem] = [
        SoundItem(nameKey: "city_ambience", fileName: "city_ambience", duration: 400),
        SoundItem(nameKey: "coffee_shop", fileName: "coffee_shop", duration: 300, isVIP: true),
        SoundItem(nameKey: "flipping_books", fileName: "flipping_books", duration: 350),
        SoundItem(nameKey: "office_ambience", fileName: "office_ambience", duration: 320),
        SoundItem(nameKey: "typing_on_keyboard", fileName: "typing_on_keyboard", duration: 320, isVIP: true)
    ]
}

// MARK: - Mix Preset
struct MixPreset: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let soundMixes: [SoundMix]
    let createdAt: Date

    init(name: String, description: String, soundMixes: [SoundMix]) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.soundMixes = soundMixes
        self.createdAt = Date()
    }

    // 用于更新现有混音的初始化方法，保持原来的 ID 和创建时间
    init(id: UUID, name: String, description: String, soundMixes: [SoundMix], createdAt: Date) {
        self.id = id
        self.name = name
        self.description = description
        self.soundMixes = soundMixes
        self.createdAt = createdAt
    }
}

// MARK: - Sound Mix
struct SoundMix: Identifiable, Codable {
    let id = UUID()
    let soundId: UUID
    let volume: Float
    let isEnabled: Bool
    
    init(soundId: UUID, volume: Float = 0.5, isEnabled: Bool = true) {
        self.soundId = soundId
        self.volume = volume
        self.isEnabled = isEnabled
    }
}

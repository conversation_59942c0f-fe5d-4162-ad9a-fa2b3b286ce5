/*
  Localizable.strings (German)
  DIYNoise
*/

// MARK: - App Name & Main
"app_name" = "DIY Noise";
"app_subtitle" = "Erstelle dein persönliches Weißes Rauschen";

// MARK: - Tab Bar
"tab_sounds" = "Klänge";
"tab_favorites" = "Favoriten";
"tab_settings" = "Einstellungen";

// MARK: - Sound Categories
"category_rain" = "Regen";
"category_rain_desc" = "Verschiedene Regengeräusche für Ruhe und Entspannung";
"category_thunder" = "Donner";
"category_thunder_desc" = "Gewittergeräusche für dramatische Atmosphäre";
"category_wind" = "Wind";
"category_wind_desc" = "Von sanfter Brise bis starkem Sturm";
"category_ocean" = "Ozean";
"category_ocean_desc" = "Beruhigende Meereswellen";
"category_birds" = "Vögel";
"category_birds_desc" = "Melodischer Gesang verschiedener Vögel";
"category_forest" = "Wald";
"category_forest_desc" = "Natürliche Waldgeräusche";
"category_fire" = "Feuer";
"category_fire_desc" = "Wärmendes Knistern eines Lagerfeuers";
"category_city" = "Stadt";
"category_city_desc" = "Urbane Hintergrundgeräusche";
"category_insects" = "Insekten";
"category_insects_desc" = "Insektengeräusche der Natur";

// MARK: - Sound Names
"rain_from_eaves" = "Regen unter der Dachrinne";
"light_rain" = "Leichter Regen";
"heavy_rain" = "Starker Regen";
"distant_thunder" = "Ferner Donner";
"thunder_rumbles" = "Donnergrollen";
"high_altitude_thunder" = "Höhengewitter";
"undulating_thunder" = "Wellenförmiger Donner";
"blows_leaves" = "Blätter im Wind";
"strong_wind" = "Starker Wind";
"winter_cold_wind" = "Winterkalter Wind";
"desert_wind" = "Wüstenwind";
"breeze_rustles" = "Raschelnde Brise";
"breeze_leaves" = "Blätter in der Brise";
"water_wave" = "Wasserwelle";
"sea_wave" = "Meereswelle";
"waves_on_shore" = "Wellen am Ufer";
"bubbling_underwater" = "Unterwasserblasen";
"small_stream" = "Kleiner Bach";
"morning_birds" = "Morgenvögel";
"forest_birds" = "Waldvögel";
"nightingale" = "Nachtigall";
"bird_chirping" = "Vogelgezwitscher";
"bird_outside" = "Vogel draußen";
"distant_bird" = "Ferner Vogel";
"cuckoo" = "Kuckuck";
"cricket_chirping" = "Grillenzirpen";
"midsummer_insect_chirping" = "Sommerinsekten";
"frog_sounds" = "Froschgeräusche";
"forest_insects" = "Waldissekten";
"summer_evening_frog" = "Sommerabend-Frösche";
"cicadas_chirping" = "Zikadengesang";
"bees_flying" = "Fliegende Bienen";
"campfire" = "Lagerfeuer";
"fire_crackling" = "Feuerknacken";
"flame_sound" = "Flammergefäusch";
"city_ambience" = "Stadtambiente";
"coffee_shop" = "Café";
"flipping_books" = "Blättern in Büchern";
"office_ambience" = "Büro";
"typing_on_keyboard" = "Tastaturtippen";

"sound_rain_from_eaves" = "Regen unter der Dachrinne";
"sound_rain_on_leaves" = "Regen auf Blättern";
"sound_light_rain" = "Leichter Regen";
"sound_heavy_rain" = "Starker Regen";
"sound_rain_with_thunder" = "Regen mit Donner";
"sound_distant_thunder" = "Ferner Donner";
"sound_close_thunder" = "Naher Donner";
"sound_thunderstorm" = "Gewitter";
"sound_lightning_thunder" = "Blitz und Donner";
"sound_gentle_breeze" = "Sanfte Brise";
"sound_strong_wind" = "Starker Wind";
"sound_wind_through_trees" = "Wind in den Bäumen";
"sound_mountain_wind" = "Bergwind";
"sound_waves_on_shore" = "Wellen am Ufer";
"sound_deep_ocean_waves" = "Tiefseewellen";
"sound_beach_waves" = "Strandwellen";
"sound_seagulls_and_waves" = "Möwen und Wellen";
"sound_morning_birds" = "Morgenvögel";
"sound_forest_birds" = "Waldvögel";
"sound_nightingale" = "Nachtigall";
"sound_bird_chirping" = "Vogelgezwitscher";
"sound_forest_ambience" = "Waldambiente";
"sound_rustling_leaves" = "Raschelnde Blätter";
"sound_forest_stream" = "Waldbach";
"sound_forest_insects" = "Waldissekten";
"sound_campfire" = "Lagerfeuer";
"sound_fireplace" = "Kamin";
"sound_wood_burning" = "Brennholz";
"sound_city_ambience" = "Stadtambiente";
"sound_cafe" = "Café";
"sound_library" = "Bibliothek";
"sound_office_ambience" = "Büro";
"sound_cricket_chirping" = "Grillenzirpen";
"sound_midsummer_insect_chirping" = "Sommerinsekten";
"sound_frog_sounds" = "Froschgeräusche";
"sound_thunder_rumbles" = "Donnergrollen";
"sound_high_altitude_thunder" = "Höhengewitter";
"sound_undulating_thunder" = "Wellenförmiger Donner";
"sound_blows_leaves" = "Blätter im Wind";
"sound_winter_cold_wind" = "Winterkalter Wind";
"sound_desert_wind" = "Wüstenwind";
"sound_breeze_rustles" = "Raschelnde Brise";
"sound_breeze_leaves" = "Blätter in der Brise";
"sound_water_wave" = "Wasserwelle";
"sound_sea_wave" = "Meereswelle";
"sound_bubbling_underwater" = "Unterwasserblasen";
"sound_small_stream" = "Kleiner Bach";
"sound_bird_outside" = "Vogel draußen";
"sound_distant_bird" = "Ferner Vogel";
"sound_cuckoo" = "Kuckuck";
"sound_summer_evening_frog" = "Sommerabend-Frösche";
"sound_cicadas_chirping" = "Zikadengesang";
"sound_bees_flying" = "Fliegende Bienen";
"sound_fire_crackling" = "Feuerknacken";
"sound_flame_sound" = "Flammergefäusch";
"sound_coffee_shop" = "Café";
"sound_flipping_books" = "Blättern in Büchern";
"sound_typing_on_keyboard" = "Tastaturtippen";

"sound_traffic" = "Verkehr";
"sound_cafe" = "Café";
"sound_subway" = "U-Bahn";
"sound_construction" = "Baustelle";
"sound_office" = "Büro";
"sound_restaurant" = "Restaurant";
"sound_library" = "Bibliothek";
"sound_airport" = "Flughafen";

"sound_white_noise" = "Weißes Rauschen";
"sound_pink_noise" = "Rosa Rauschen";
"sound_brown_noise" = "Braunes Rauschen";
"sound_blue_noise" = "Blaues Rauschen";
"sound_violet_noise" = "Violettes Rauschen";
"sound_grey_noise" = "Graues Rauschen";

"sound_space" = "Weltraum";
"sound_underwater" = "Unterwasser";
"sound_cave" = "Höhle";
"sound_meditation" = "Meditation";
"sound_temple" = "Tempel";
"sound_monastery" = "Kloster";

// MARK: - Sound Descriptions
"rain_from_eaves_desc" = "Regen, der von der Dachrinne tropft";
"light_rain_desc" = "Sanftes Geräusch von leichtem Regen";
"heavy_rain_desc" = "Intensives Geräusch von starkem Regen";
"distant_thunder_desc" = "Donnergrollen in der Ferne";
"thunder_rumbles_desc" = "Tiefes, grollendes Donnergeräusch";
"high_altitude_thunder_desc" = "Donner aus großer Höhe";
"undulating_thunder_desc" = "Wellenförmiges Donnergeräusch";
"blows_leaves_desc" = "Wind, der durch Blätter weht";
"strong_wind_desc" = "Geräusch von starkem Wind";
"winter_cold_wind_desc" = "Kalter Winterwind";
"desert_wind_desc" = "Trockener Wüstenwind";
"breeze_rustles_desc" = "Sanftes Rascheln einer Brise";
"breeze_leaves_desc" = "Brise, die Blätter streift";
"water_wave_desc" = "Sanftes Plätschern von Wasserwellen";
"sea_wave_desc" = "Rauschen von Meereswellen";
"waves_on_shore_desc" = "Wellen, die ans Ufer schlagen";
"bubbling_underwater_desc" = "Blubbernde Blasen unter Wasser";
"small_stream_desc" = "Plätschern eines kleinen Bachs";
"morning_birds_desc" = "Morgenkonzert der Vögel";
"forest_birds_desc" = "Vogelgesang im Wald";
"nightingale_desc" = "Melodischer Gesang der Nachtigall";
"bird_chirping_desc" = "Verschiedenes Vogelgezwitscher";
"bird_outside_desc" = "Vogelgesang draußen";
"distant_bird_desc" = "Vogelruf in der Ferne";
"cuckoo_desc" = "Ruf des Kuckucks";
"cricket_chirping_desc" = "Zirpen der Grillen in der Nacht";
"midsummer_insect_chirping_desc" = "Sommerliche Insektengeräusche";
"frog_sounds_desc" = "Friedliches Froschquaken";
"forest_insects_desc" = "Insektengeräusche im Wald";
"summer_evening_frog_desc" = "Froschkonzert an Sommerabenden";
"cicadas_chirping_desc" = "Zikadengesang im Sommer";
"bees_flying_desc" = "Summen fliegender Bienen";
"campfire_desc" = "Knackendes Lagerfeuer";
"fire_crackling_desc" = "Knisterndes Feuergeräusch";
"flame_sound_desc" = "Stetiges Flammenrauschen";
"city_ambience_desc" = "Hintergrundgeräusche der Stadt";
"coffee_shop_desc" = "Café-Ambiente";
"flipping_books_desc" = "Geräusch von umblätternden Seiten";
"office_ambience_desc" = "Büro-Hintergrundgeräusche";
"typing_on_keyboard_desc" = "Tastaturgeräusche";

"sound_rain_on_roof_desc" = "Regen auf dem Dach";
"sound_rain_on_leaves_desc" = "Regen auf Blättern";
"sound_light_rain_desc" = "Sanftes Geräusch von leichtem Regen";
"sound_heavy_rain_desc" = "Intensives Geräusch von starkem Regen";
"sound_rain_with_thunder_desc" = "Regen mit fernem Donner";
"sound_distant_thunder_desc" = "Donnergrollen in der Ferne";
"sound_close_thunder_desc" = "Donner in der Nähe";
"sound_thunderstorm_desc" = "Perfekte Kombination aus Donner und Regen";
"sound_lightning_thunder_desc" = "Donner nach einem Blitz";
"sound_gentle_breeze_desc" = "Sanftes Wehen einer Brise";
"sound_strong_wind_desc" = "Geräusch von starkem Wind";
"sound_wind_through_trees_desc" = "Wind durch die Bäume";
"sound_mountain_wind_desc" = "Bergwind";
"sound_waves_on_shore_desc" = "Wellen, die ans Ufer schlagen";
"sound_deep_ocean_waves_desc" = "Wellen in der Tiefsee";
"sound_beach_waves_desc" = "Wellen am Strand";
"sound_seagulls_and_waves_desc" = "Möwen und Wellen";
"sound_morning_birds_desc" = "Morgenkonzert der Vögel";
"sound_forest_birds_desc" = "Vogelgesang im Wald";
"sound_nightingale_desc" = "Melodischer Gesang der Nachtigall";
"sound_bird_chirping_desc" = "Verschiedenes Vogelgezwitscher";
"sound_forest_ambience_desc" = "Wald-Hintergrundgeräusche";
"sound_rustling_leaves_desc" = "Raschelnde Blätter";
"sound_forest_stream_desc" = "Bach im Wald";
"sound_forest_insects_desc" = "Insektengeräusche im Wald";
"sound_campfire_desc" = "Knackendes Lagerfeuer";
"sound_fireplace_desc" = "Kamingeräusch";
"sound_wood_burning_desc" = "Brennholzgeräusch";
"sound_city_ambience_desc" = "Hintergrundgeräusche der Stadt";
"sound_cafe_desc" = "Café-Ambiente";
"sound_library_desc" = "Ruhige Bibliotheksatmosphäre";
"sound_office_ambience_desc" = "Büro-Hintergrundgeräusche";
"sound_midsummer_insect_chirping_desc" = "Sommerliche Insektengeräusche";
"sound_frog_sounds_desc" = "Friedliches Froschquaken";
"sound_thunder_rumbles_desc" = "Tiefes, grollendes Donnergeräusch";
"sound_high_altitude_thunder_desc" = "Donner aus großer Höhe";
"sound_undulating_thunder_desc" = "Wellenförmiges Donnergeräusch";
"sound_blows_leaves_desc" = "Wind, der Blätter draußen bewegt";
"sound_winter_cold_wind_desc" = "Kalter Winterwind";
"sound_desert_wind_desc" = "Trockener Wüstenwind";
"sound_breeze_rustles_desc" = "Sanftes Rascheln einer Brise";
"sound_breeze_leaves_desc" = "Brise, die Blätter streift";
"sound_water_wave_desc" = "Sanftes Plätschern von Wasserwellen";
"sound_sea_wave_desc" = "Rauschen von Meereswellen";
"sound_bubbling_underwater_desc" = "Blubbernde Blasen unter Wasser";
"sound_small_stream_desc" = "Plätschern eines kleinen Bachs";
"sound_bird_outside_desc" = "Vogelgesang draußen";
"sound_distant_bird_desc" = "Vogelruf in der Ferne";
"sound_cuckoo_desc" = "Ruf des Kuckucks";
"sound_summer_evening_frog_desc" = "Froschkonzert an Sommerabenden";
"sound_cicadas_chirping_desc" = "Zikadengesang im Sommer";
"sound_bees_flying_desc" = "Summen fliegender Bienen";
"sound_fire_crackling_desc" = "Knisterndes Feuergeräusch";
"sound_flame_sound_desc" = "Stetiges Flammenrauschen";
"sound_coffee_shop_desc" = "Café-Ambiente";
"sound_flipping_books_desc" = "Geräusch von umblätternden Seiten";
"sound_typing_on_keyboard_desc" = "Tastaturgeräusche";
"sound_rain_from_eaves_desc" = "Regen, der von der Dachrinne auf den Boden tropft";
"sound_cricket_chirping_desc" = "Leises Grillenzirpen";

"sound_traffic_desc" = "Hintergrundverkehrsgeräusche";
"sound_cafe_desc" = "Café-Ambiente";
"sound_subway_desc" = "U-Bahn-Geräusche";
"sound_construction_desc" = "Baustellengeräusche";
"sound_office_desc" = "Büro-Ambiente";
"sound_restaurant_desc" = "Restaurant-Hintergrund";
"sound_library_desc" = "Ruhige Bibliotheksgeräusche";
"sound_airport_desc" = "Flughafenambiente";

"sound_white_noise_desc" = "Klassisches Weißes Rauschen";
"sound_pink_noise_desc" = "Sanftes Rosa Rauschen";
"sound_brown_noise_desc" = "Tiefes Braunes Rauschen";
"sound_blue_noise_desc" = "Klares Blaues Rauschen";
"sound_violet_noise_desc" = "Hochfrequentes Violettes Rauschen";
"sound_grey_noise_desc" = "Ausgewogenes Graues Rauschen";

"sound_space_desc" = "Mysteriöse Weltraumgeräusche";
"sound_underwater_desc" = "Unterwasserstille";
"sound_cave_desc" = "Höhlenecho";
"sound_meditation_desc" = "Hintergrund für Meditation";
"sound_temple_desc" = "Tempelglocken";
"sound_monastery_desc" = "Klosterruhe";

// MARK: - UI Elements
"sound_count" = "%d Klänge";
"playing_count" = "%d/%d werden abgespielt";
"volume" = "Lautstärke";

// MARK: - Buttons
"play" = "Abspielen";
"pause" = "Pause";
"stop" = "Stopp";
"stop_all" = "Alle stoppen";
"save" = "Speichern";
"cancel" = "Abbrechen";
"delete" = "Löschen";
"edit" = "Bearbeiten";
"done" = "Fertig";
"close" = "Schließen";
"back" = "Zurück";

// MARK: - Mixer
"mixer_title" = "Mixer";
"active_sounds_count" = "%d aktive Klänge";
"no_active_sounds" = "Keine aktiven Klänge";
"no_active_sounds_desc" = "Zurück zur Hauptseite, um Klänge auszuwählen und mit dem Mischen zu beginnen";
"save_mix" = "Mix speichern";
"save_as_new_mix" = "Als neuen Mix speichern";
"update_current_mix" = "Speichern";
"update_and_add_sounds" = "Aktualisieren und Klänge hinzufügen";
"update_mix" = "Mix aktualisieren";
"update_mix_prompt" = "Diesen Mix mit aktuellen Einstellungen aktualisieren?";
"saved_mixes" = "Gespeicherte Mixe";
"mix_name" = "Mix-Name";
"mix_description" = "Beschreibung";
"mix_name_placeholder" = "Mix-Name";
"mix_description_placeholder" = "Beschreibung (optional)";
"mix_name_prompt" = "Gib deinem aktuellen Mix einen Namen";
"unnamed_mix" = "Unbenannter Mix";

// MARK: - Favorites
"favorites_title" = "Meine Favoriten";
"favorites_subtitle" = "Gespeicherte Klangmixe";
"favorites_count" = "Favoriten";
"no_favorites" = "Keine Favoriten";
"no_favorites_desc" = "Speichere deine Lieblingsklangkombinationen im Mixer";
"delete_favorite" = "Favorit löschen";
"delete_favorite_message" = "Diesen Favoriten-Mix wirklich löschen?";
"mix_sounds_count" = "%d Klänge";
"mix_combination" = "Mix-Kombination";

// MARK: - Settings
"settings_title" = "Einstellungen";
"settings_subtitle" = "Passe deine App an";
"appearance_section" = "Darstellung";
"language_section" = "Sprache";
"playback_section" = "Wiedergabe";
"about_section" = "Über";

// Darstellung
"theme_color" = "Themenfarbe";
"dark_mode" = "Dunkler Modus";
"follow_system" = "System folgen";

// Sprache
"app_language" = "App-Sprache";
"system_language_settings" = "Zu Systemeinstellungen";

// Wiedergabe
"auto_play" = "Automatische Wiedergabe";
"auto_play_desc" = "Letzten Mix beim App-Start automatisch fortsetzen";
"background_play" = "Hintergrundwiedergabe";
"background_play_desc" = "Weiter abspielen, wenn App im Hintergrund ist";
"lock_screen_control" = "Sperrbildschirm-Steuerung";
"lock_screen_control_desc" = "Steuerung direkt vom Sperrbildschirm und Kontrollzentrum";

// Lock Screen Control VIP
"lock_screen_control_title" = "Sperrbildschirm-Steuerung";
"lock_screen_control_vip_desc" = "Upgrade auf VIP, um von Sperrbildschirm und Kontrollzentrum zu steuern";
"lock_screen_control_feature" = "Sperrbildschirm-Steuerung";
"lock_screen_control_feature_desc" = "Steuerung direkt vom Sperrbildschirm";
"lock_screen_control_benefit1" = "Steuerung ohne Gerät zu entsperren";
"lock_screen_control_benefit2" = "Schneller Zugriff vom Kontrollzentrum";
"lock_screen_control_benefit3" = "Nahtlose Integration mit iOS-Mediensteuerung";

// Auto Play Messages
"auto_play_enabled" = "Automatische Wiedergabe aktiviert";
"auto_play_disabled" = "Automatische Wiedergabe deaktiviert";
"no_last_mix_found" = "Kein letzter Mix gefunden";
"last_mix_loaded" = "Letzter Mix geladen";
"auto_play_mix_name" = "Auto-Play-Mix";
"auto_play_mix_desc" = "Für automatische Wiedergabe gespeicherter Mix";

// Background Play Messages
"background_play_enabled" = "Hintergrundwiedergabe aktiviert";
"background_play_disabled" = "Hintergrundwiedergabe deaktiviert";

// Über
"version" = "Version";
"feedback" = "Feedback";
"feedback_desc" = "Sende Feedback und Vorschläge";
"rate_app" = "Bewerten";
"rate_app_desc" = "Bewerte im App Store";

// MARK: - Timer
"timer" = "Timer";
"timer_remaining" = "Verbleibend %@";
"set_timer" = "Timer einstellen";
"timer_minutes" = "Minuten";
"timer_hours" = "Stunden";

// MARK: - Alerts & Messages
"error" = "Fehler";
"success" = "Erfolg";
"loading" = "Lädt...";
"saved_successfully" = "Erfolgreich gespeichert";
"deleted_successfully" = "Erfolgreich gelöscht";

// MARK: - Accessibility
"play_button" = "Abspielen-Taste";
"pause_button" = "Pause-Taste";
"volume_slider" = "Lautstärkeregler";
"close_button" = "Schließen-Taste";

// MARK: - VIP Subscription
// VIP Feature Titles
"vip_sound_title" = "VIP-exklusive Klänge";
"mix_limit_title" = "Unbegrenztes Mischen";
"save_limit_title" = "Unbegrenztes Speichern";
"vip_theme_title" = "VIP-exklusive Themes";
"long_timer_title" = "Langer Timer";

// VIP Feature Descriptions
"vip_sound_desc" = "Dieser Klang erfordert VIP-Mitgliedschaft. Upgrade auf VIP für alle hochwertigen exklusiven Klänge!";
"mix_limit_desc" = "Kostenlose Nutzer können nur %d Klänge gleichzeitig abspielen. Upgrade für unbegrenztes Mischen!";
"save_limit_desc" = "Kostenlose Nutzer können nur %d Favoriten speichern. Upgrade für unbegrenztes Speichern!";
"vip_theme_desc" = "Dieses Theme erfordert VIP-Mitgliedschaft. Upgrade für alle schönen Themes!";
"long_timer_desc" = "Kostenlose Nutzer können Timer nur bis 30 Minuten setzen. Upgrade für längere Timer!";

// VIP Benefits
"upgrade_will_get" = "Mit dem Upgrade erhältst du:";
"all_vip_benefits" = "Alle VIP-Vorteile";

// VIP Sound Benefits
"vip_sound_benefit_1" = "Alle VIP-exklusiven Klänge freischalten";
"vip_sound_benefit_2" = "Hochwertiges Audio-Erlebnis";
"vip_sound_benefit_3" = "Exklusive Klanginhalte";

// Mix Limit Benefits
"mix_limit_benefit_1" = "Unbegrenzt Klänge zum Mix hinzufügen";
"mix_limit_benefit_2" = "Komplexe Klangkombinationen erstellen";
"mix_limit_benefit_3" = "Professionelles Mix-Erlebnis";

// Save Limit Benefits
"save_limit_benefit_1" = "Unbegrenzt Favoriten speichern";
"save_limit_benefit_2" = "Persönliche Klangbibliothek erstellen";
"save_limit_benefit_3" = "Jederzeit auf Lieblingsmixe zugreifen";

// VIP Theme Benefits
"vip_theme_benefit_1" = "Alle schönen Themes freischalten";
"vip_theme_benefit_2" = "Personalisiertes Interface-Erlebnis";
"vip_theme_benefit_3" = "Exklusives visuelles Design";

// Long Timer Benefits
"long_timer_benefit_1" = "Timer über 30 Minuten einstellen";
"long_timer_benefit_2" = "Entspannungszeit verlängern";
"long_timer_benefit_3" = "Perfekt für Schlaf und Meditation";

// VIP Feature List (for all features view)
"vip_sound_feature" = "VIP-exklusive Klänge";
"vip_sound_feature_desc" = "Hochwertige exklusive Klänge freischalten";
"mix_limit_feature" = "Unbegrenztes Mischen";
"mix_limit_feature_desc" = "Unbegrenzt Klänge zum Mix hinzufügen";
"save_limit_feature" = "Unbegrenztes Speichern";
"save_limit_feature_desc" = "Unbegrenzt Favoriten speichern";
"vip_theme_feature" = "VIP-exklusive Themes";
"vip_theme_feature_desc" = "Alle schönen Themes verwenden";
"long_timer_feature" = "Langer Timer";
"long_timer_feature_desc" = "Timer über 30 Minuten einstellen";
"lock_screen_control_feature" = "Sperrbildschirm-Steuerung";
"lock_screen_control_feature_desc" = "Steuerung direkt vom Sperrbildschirm";

// Action Buttons
"upgrade_to_vip" = "Zu VIP upgraden";
"later_button" = "Später";
"tap_to_upgrade" = "Zum Upgrade tippen";

// MARK: - Theme Names
"theme_ocean_breeze" = "Meeresbrise";
"theme_sunset_glow" = "Abendrot";
"theme_forest_mist" = "Waldnebel";
"theme_night_sky" = "Nachthimmel";
"theme_lavender_dream" = "Lavendeltraum";
"theme_fire_ember" = "Feuerglut";

// MARK: - Theme Selector
"select_theme" = "Zum Verwenden auswählen";
"current_theme" = "Aktuell: %@";
"theme_in_use" = "In Benutzung";
"previous_theme" = "Vorheriges";
"next_theme" = "Nächstes";

// Theme Change Confirmation
"confirm_theme_change" = "Theme-Änderung bestätigen";
"confirm_theme_change_message" = "Zum Theme \"%@\" wechseln?";
"confirm" = "Bestätigen";

// Favorite Delete Confirmation
"delete_favorite_confirm" = "Favoriten-Mix löschen";
"delete_favorite_message" = "Der letzte Klang zu löschen, löscht auch den Favoriten-Mix \"%@\". Fortfahren?";
"delete_favorite" = "Favorit löschen";

// MARK: - Timer
"timer" = "Timer";
"time_remaining" = "Verbleibende Zeit";
"set_timer" = "Timer einstellen";
"timer_description" = "Wähle die Abspieldauer, stoppt automatisch nach Ablauf";
"quick_select" = "Schnellauswahl";
"custom_time" = "Benutzerdefinierte Zeit";
"hours" = "Stunden";
"minutes" = "Minuten";
"seconds" = "Sekunden";
"start_timer" = "Timer starten";
"cancel_timer" = "Timer abbrechen";
"elapsed_time" = "%@ abgespielt";

// Timer durations
"timer_15min" = "15 Minuten";
"timer_30min" = "30 Minuten";
"timer_45min" = "45 Minuten";
"timer_1hour" = "1 Stunde";
"timer_1_5hour" = "1.5 Stunden";
"timer_2hour" = "2 Stunden";

// Now Playing Info
"playing_count" = "%d/%d Klänge werden abgespielt";
"active_sounds_count" = "%d aktive Klänge";
"app_subtitle" = "Ambient-Klang-Mixer";

// MARK: - Subscription Plans
"monthly_subscription" = "Monatliches Abo";
"yearly_subscription" = "Jährliches Abo";
"lifetime_purchase" = "Lebenslanger Kauf";
"popular_badge" = "Beliebt";
"best_value_badge" = "Bester Wert";
"one_time_purchase" = "Einmalkauf";
"cancel_anytime" = "Jederzeit kündbar";
"save_over_30_percent" = "Spare über 30% im Vergleich zum Monatsabo";
"lifetime_description" = "Einmal kaufen, alle VIP-Funktionen lebenslang nutzen";
"choose_subscription_plan" = "Abonnementplan wählen";
"loading_subscription_options" = "Lade Abonnementoptionen...";
"restore_purchases" = "Käufe wiederherstellen";
"terms_of_service" = "Nutzungsbedingungen";
"privacy_policy" = "Datenschutzrichtlinie";
"subscription_success" = "Abonnement erfolgreich! Vielen Dank für Ihre Unterstützung!";
"purchase_failed" = "Kauf fehlgeschlagen: %@";
"restore_success" = "Käufe wiederhergestellt!";
"no_purchases_to_restore" = "Keine wiederherstellbaren Käufe";
"processing" = "Wird verarbeitet...";

// MARK: - Subscription Interface
"upgrade_to_vip_title" = "Zu VIP upgraden";
"upgrade_subtitle" = "Schalte alle Premium-Funktionen frei und erlebe DIY Noise vollständig";
"vip_membership_benefits" = "VIP-Mitgliedschaftsvorteile";
"preview_mode_notice" = "Vorschaumodus - App Store Connect Produkte konfigurieren";

// Subscription Period
"daily" = "Täglich";
"weekly" = "Wöchentlich";
"monthly" = "Monatlich";
"yearly" = "Jährlich";

// VIP Features (Reiterated for subscription screen)
"vip_exclusive_sounds" = "VIP-exklusive Klänge";
"vip_exclusive_sounds_desc" = "Schalte alle hochwertigen exklusiven Klänge frei";
"unlimited_mixing" = "Unbegrenztes Mischen";
"unlimited_mixing_desc" = "Füge unbegrenzt Klänge zum Mix hinzu";
"unlimited_favorites" = "Unbegrenztes Speichern";
"unlimited_favorites_desc" = "Speichere unbegrenzt Favoriten-Mixe";
"vip_exclusive_themes" = "VIP-exklusive Themes";
"vip_exclusive_themes_desc" = "Verwende alle schönen Interface-Themes";
"lock_screen_control_feature" = "Sperrbildschirm-Steuerung";
"lock_screen_control_feature_desc" = "Steuerung direkt vom Sperrbildschirm";
"extended_timer" = "Langer Timer";
"extended_timer_desc" = "Timer über 30 Minuten";

// Alert
"alert_title" = "Hinweis";
"ok_button" = "OK";

/*
  Localizable.strings (French)
  DIYNoise
*/

// MARK: - App Name & Main
"app_name" = "DIY Noise";
"app_subtitle" = "Créez votre bruit blanc personnalisé";

// MARK: - Tab Bar
"tab_sounds" = "Sons";
"tab_favorites" = "Favoris";
"tab_settings" = "Paramètres";

// MARK: - Sound Categories
"category_rain" = "Pluie";
"category_rain_desc" = "Différents sons de pluie pour une sensation de calme";
"category_thunder" = "Tonnerre";
"category_thunder_desc" = "Bruits de tonnerre pour une atmosphère dramatique";
"category_wind" = "Vent";
"category_wind_desc" = "Du vent léger à la tempête";
"category_ocean" = "Océan";
"category_ocean_desc" = "Bruit apaisant des vagues";
"category_birds" = "Oiseaux";
"category_birds_desc" = "Chants mélodieux de différents oiseaux";
"category_forest" = "Forêt";
"category_forest_desc" = "Sons naturels de la forêt";
"category_fire" = "Feu";
"category_fire_desc" = "Crépitement chaleureux d'un feu";
"category_city" = "Ville";
"category_city_desc" = "Ambiance sonore urbaine";
"category_insects" = "Insectes";
"category_insects_desc" = "Sons d'insectes dans la nature";

// MARK: - Sound Names
"rain_from_eaves" = "Pluie sous les gouttières";
"light_rain" = "Pluie légère";
"heavy_rain" = "Pluie intense";
"distant_thunder" = "Tonnerre lointain";
"thunder_rumbles" = "Roulement de tonnerre";
"high_altitude_thunder" = "Tonnerre en altitude";
"undulating_thunder" = "Tonnerre ondulant";
"blows_leaves" = "Vent dans les feuilles";
"strong_wind" = "Vent fort";
"winter_cold_wind" = "Vent froid hivernal";
"desert_wind" = "Vent du désert";
"breeze_rustles" = "Brise bruissante";
"breeze_leaves" = "Brise dans les feuilles";
"water_wave" = "Vague d'eau";
"sea_wave" = "Vague marine";
"waves_on_shore" = "Vagues sur le rivage";
"bubbling_underwater" = "Bulles sous-marines";
"small_stream" = "Petit ruisseau";
"morning_birds" = "Oiseaux du matin";
"forest_birds" = "Oiseaux de forêt";
"nightingale" = "Rossignol";
"bird_chirping" = "Gazouillis d'oiseaux";
"bird_outside" = "Oiseau à la fenêtre";
"distant_bird" = "Oiseau lointain";
"cuckoo" = "Coucou";
"cricket_chirping" = "Grillons";
"midsummer_insect_chirping" = "Insectes d'été";
"frog_sounds" = "Grenouilles";
"forest_insects" = "Insectes forestiers";
"summer_evening_frog" = "Grenouilles en soirée d'été";
"cicadas_chirping" = "Cigales";
"bees_flying" = "Abeilles volantes";
"campfire" = "Feu de camp";
"fire_crackling" = "Crépitement du feu";
"flame_sound" = "Bruit de flamme";
"city_ambience" = "Ambiance urbaine";
"coffee_shop" = "Café";
"flipping_books" = "Pages tournées";
"office_ambience" = "Bureau";
"typing_on_keyboard" = "Frappe au clavier";

"sound_rain_from_eaves" = "Pluie sous les gouttières";
"sound_rain_on_leaves" = "Pluie sur les feuilles";
"sound_light_rain" = "Pluie légère";
"sound_heavy_rain" = "Pluie intense";
"sound_rain_with_thunder" = "Pluie et tonnerre";
"sound_distant_thunder" = "Tonnerre lointain";
"sound_close_thunder" = "Tonnerre proche";
"sound_thunderstorm" = "Orage";
"sound_lightning_thunder" = "Éclair et tonnerre";
"sound_gentle_breeze" = "Brise légère";
"sound_strong_wind" = "Vent fort";
"sound_wind_through_trees" = "Vent dans les arbres";
"sound_mountain_wind" = "Vent de montagne";
"sound_waves_on_shore" = "Vagues sur le rivage";
"sound_deep_ocean_waves" = "Vagues profondes";
"sound_beach_waves" = "Vagues de plage";
"sound_seagulls_and_waves" = "Mouettes et vagues";
"sound_morning_birds" = "Oiseaux du matin";
"sound_forest_birds" = "Oiseaux de forêt";
"sound_nightingale" = "Rossignol";
"sound_bird_chirping" = "Gazouillis d'oiseaux";
"sound_forest_ambience" = "Ambiance forestière";
"sound_rustling_leaves" = "Feuilles bruissantes";
"sound_forest_stream" = "Ruisseau forestier";
"sound_forest_insects" = "Insectes forestiers";
"sound_campfire" = "Feu de camp";
"sound_fireplace" = "Cheminée";
"sound_wood_burning" = "Bois qui brûle";
"sound_city_ambience" = "Ambiance urbaine";
"sound_cafe" = "Café";
"sound_library" = "Bibliothèque";
"sound_office_ambience" = "Bureau";
"sound_cricket_chirping" = "Grillons";
"sound_midsummer_insect_chirping" = "Insectes d'été";
"sound_frog_sounds" = "Grenouilles";
"sound_thunder_rumbles" = "Roulement de tonnerre";
"sound_high_altitude_thunder" = "Tonnerre en altitude";
"sound_undulating_thunder" = "Tonnerre ondulant";
"sound_blows_leaves" = "Vent dans les feuilles";
"sound_winter_cold_wind" = "Vent froid hivernal";
"sound_desert_wind" = "Vent du désert";
"sound_breeze_rustles" = "Brise bruissante";
"sound_breeze_leaves" = "Brise dans les feuilles";
"sound_water_wave" = "Vague d'eau";
"sound_sea_wave" = "Vague marine";
"sound_bubbling_underwater" = "Bulles sous-marines";
"sound_small_stream" = "Petit ruisseau";
"sound_bird_outside" = "Oiseau à la fenêtre";
"sound_distant_bird" = "Oiseau lointain";
"sound_cuckoo" = "Coucou";
"sound_summer_evening_frog" = "Grenouilles en soirée d'été";
"sound_cicadas_chirping" = "Cigales";
"sound_bees_flying" = "Abeilles volantes";
"sound_fire_crackling" = "Crépitement du feu";
"sound_flame_sound" = "Bruit de flamme";
"sound_coffee_shop" = "Café";
"sound_flipping_books" = "Pages tournées";
"sound_typing_on_keyboard" = "Frappe au clavier";

"sound_traffic" = "Trafic";
"sound_cafe" = "Café";
"sound_subway" = "Métro";
"sound_construction" = "Chantier";
"sound_office" = "Bureau";
"sound_restaurant" = "Restaurant";
"sound_library" = "Bibliothèque";
"sound_airport" = "Aéroport";

"sound_white_noise" = "Bruit blanc";
"sound_pink_noise" = "Bruit rose";
"sound_brown_noise" = "Bruit brun";
"sound_blue_noise" = "Bruit bleu";
"sound_violet_noise" = "Bruit violet";
"sound_grey_noise" = "Bruit gris";

"sound_space" = "Espace";
"sound_underwater" = "Sous-marin";
"sound_cave" = "Grotte";
"sound_meditation" = "Méditation";
"sound_temple" = "Temple";
"sound_monastery" = "Monastère";

// MARK: - Sound Descriptions
"rain_from_eaves_desc" = "Son de la pluie tombant des gouttières";
"light_rain_desc" = "Son d'une pluie douce et légère";
"heavy_rain_desc" = "Son d'une pluie forte et intense";
"distant_thunder_desc" = "Son du tonnerre au loin";
"thunder_rumbles_desc" = "Son profond du tonnerre qui gronde";
"high_altitude_thunder_desc" = "Son du tonnerre venant de haut";
"undulating_thunder_desc" = "Son du tonnerre qui ondule";
"blows_leaves_desc" = "Son du vent soufflant dans les feuilles";
"strong_wind_desc" = "Son d'un vent puissant";
"winter_cold_wind_desc" = "Son du vent froid de l'hiver";
"desert_wind_desc" = "Son du vent sec du désert";
"breeze_rustles_desc" = "Son doux d'une brise légère";
"breeze_leaves_desc" = "Son d'une brise caressant les feuilles";
"water_wave_desc" = "Son doux des vagues d'eau";
"sea_wave_desc" = "Son des vagues de la mer";
"waves_on_shore_desc" = "Son des vagues sur la rive";
"bubbling_underwater_desc" = "Son des bulles sous l'eau";
"small_stream_desc" = "Son d'un petit ruisseau qui coule";
"morning_birds_desc" = "Chorale des oiseaux du matin";
"forest_birds_desc" = "Chants d'oiseaux en forêt";
"nightingale_desc" = "Chant mélodieux du rossignol";
"bird_chirping_desc" = "Gazouillis variés d'oiseaux";
"bird_outside_desc" = "Chant d'oiseau à l'extérieur";
"distant_bird_desc" = "Chant d'oiseau au loin";
"cuckoo_desc" = "Chant du coucou";
"cricket_chirping_desc" = "Son des grillons la nuit";
"midsummer_insect_chirping_desc" = "Sons variés d'insectes en été";
"frog_sounds_desc" = "Coassements paisibles de grenouilles";
"forest_insects_desc" = "Sons d'insectes en forêt";
"summer_evening_frog_desc" = "Coassements de grenouilles en soirée d'été";
"cicadas_chirping_desc" = "Chant des cigales en été";
"bees_flying_desc" = "Bourdonnement des abeilles en vol";
"campfire_desc" = "Crépitement d'un feu de camp";
"fire_crackling_desc" = "Son des flammes qui crépitent";
"flame_sound_desc" = "Son stable d'une flamme qui brûle";
"city_ambience_desc" = "Ambiance sonore urbaine";
"coffee_shop_desc" = "Ambiance d'un café";
"flipping_books_desc" = "Son des pages qu'on tourne";
"office_ambience_desc" = "Ambiance de bureau";
"typing_on_keyboard_desc" = "Son de clavier d'ordinateur";

"sound_rain_on_roof_desc" = "Son de la pluie sur le toit";
"sound_rain_on_leaves_desc" = "Son de la pluie sur les feuilles";
"sound_light_rain_desc" = "Son d'une pluie douce et légère";
"sound_heavy_rain_desc" = "Son d'une pluie forte et intense";
"sound_rain_with_thunder_desc" = "Pluie avec tonnerre au loin";
"sound_distant_thunder_desc" = "Son du tonnerre au loin";
"sound_close_thunder_desc" = "Son du tonnerre proche";
"sound_thunderstorm_desc" = "Combinaison parfaite de pluie et tonnerre";
"sound_lightning_thunder_desc" = "Tonnerre après un éclair";
"sound_gentle_breeze_desc" = "Son d'une brise légère";
"sound_strong_wind_desc" = "Son d'un vent puissant";
"sound_wind_through_trees_desc" = "Son du vent à travers les arbres";
"sound_mountain_wind_desc" = "Son du vent en montagne";
"sound_waves_on_shore_desc" = "Son des vagues sur la rive";
"sound_deep_ocean_waves_desc" = "Son des vagues en eau profonde";
"sound_beach_waves_desc" = "Son des vagues sur la plage";
"sound_seagulls_and_waves_desc" = "Mouettes et vagues";
"sound_morning_birds_desc" = "Chorale des oiseaux du matin";
"sound_forest_birds_desc" = "Chants d'oiseaux en forêt";
"sound_nightingale_desc" = "Chant mélodieux du rossignol";
"sound_bird_chirping_desc" = "Gazouillis variés d'oiseaux";
"sound_forest_ambience_desc" = "Ambiance sonore forestière";
"sound_rustling_leaves_desc" = "Son des feuilles qui bruissent";
"sound_forest_stream_desc" = "Son d'un ruisseau en forêt";
"sound_forest_insects_desc" = "Sons d'insectes en forêt";
"sound_campfire_desc" = "Crépitement d'un feu de camp";
"sound_fireplace_desc" = "Son d'un feu de cheminée";
"sound_wood_burning_desc" = "Son du bois qui brûle";
"sound_city_ambience_desc" = "Ambiance sonore urbaine";
"sound_cafe_desc" = "Ambiance d'un café";
"sound_library_desc" = "Ambiance calme d'une bibliothèque";
"sound_office_ambience_desc" = "Ambiance de bureau";
"sound_midsummer_insect_chirping_desc" = "Sons variés d'insectes en été";
"sound_frog_sounds_desc" = "Coassements paisibles de grenouilles";
"sound_thunder_rumbles_desc" = "Son profond du tonnerre qui gronde";
"sound_high_altitude_thunder_desc" = "Son du tonnerre venant de haut";
"sound_undulating_thunder_desc" = "Son du tonnerre qui ondule";
"sound_blows_leaves_desc" = "Vent soufflant dans les feuilles";
"sound_winter_cold_wind_desc" = "Son du vent froid de l'hiver";
"sound_desert_wind_desc" = "Son du vent sec du désert";
"sound_breeze_rustles_desc" = "Son doux d'une brise légère";
"sound_breeze_leaves_desc" = "Son d'une brise caressant les feuilles";
"sound_water_wave_desc" = "Son doux des vagues d'eau";
"sound_sea_wave_desc" = "Son des vagues de la mer";
"sound_bubbling_underwater_desc" = "Son des bulles sous l'eau";
"sound_small_stream_desc" = "Son d'un petit ruisseau qui coule";
"sound_bird_outside_desc" = "Chant d'oiseau à l'extérieur";
"sound_distant_bird_desc" = "Chant d'oiseau au loin";
"sound_cuckoo_desc" = "Chant du coucou";
"sound_summer_evening_frog_desc" = "Coassements de grenouilles en soirée d'été";
"sound_cicadas_chirping_desc" = "Chant des cigales en été";
"sound_bees_flying_desc" = "Bourdonnement des abeilles en vol";
"sound_fire_crackling_desc" = "Son des flammes qui crépitent";
"sound_flame_sound_desc" = "Son stable d'une flamme qui brûle";
"sound_coffee_shop_desc" = "Ambiance d'un café";
"sound_flipping_books_desc" = "Son des pages qu'on tourne";
"sound_typing_on_keyboard_desc" = "Son de clavier d'ordinateur";
"sound_rain_from_eaves_desc" = "Pluie tombant des gouttières sur le sol";
"sound_cricket_chirping_desc" = "Son doux des grillons";

"sound_traffic_desc" = "Bruit de fond du trafic urbain";
"sound_cafe_desc" = "Ambiance sonore d'un café";
"sound_subway_desc" = "Son du métro";
"sound_construction_desc" = "Bruit de chantier";
"sound_office_desc" = "Ambiance de bureau";
"sound_restaurant_desc" = "Ambiance de restaurant";
"sound_library_desc" = "Calme d'une bibliothèque";
"sound_airport_desc" = "Ambiance d'aéroport";

"sound_white_noise_desc" = "Bruit blanc classique";
"sound_pink_noise_desc" = "Bruit rose doux";
"sound_brown_noise_desc" = "Bruit brun profond";
"sound_blue_noise_desc" = "Bruit bleu clair";
"sound_violet_noise_desc" = "Bruit violet haute fréquence";
"sound_grey_noise_desc" = "Bruit gris équilibré";

"sound_space_desc" = "Sons mystérieux de l'espace";
"sound_underwater_desc" = "Calme sous-marin";
"sound_cave_desc" = "Échos d'une grotte";
"sound_meditation_desc" = "Ambiance pour méditation";
"sound_temple_desc" = "Sons de cloches de temple";
"sound_monastery_desc" = "Tranquillité monastique";

// MARK: - UI Elements
"sound_count" = "%d sons";
"playing_count" = "%d/%d en lecture";
"volume" = "Volume";

// MARK: - Buttons
"play" = "Lire";
"pause" = "Pause";
"stop" = "Arrêter";
"stop_all" = "Tout arrêter";
"save" = "Enregistrer";
"cancel" = "Annuler";
"delete" = "Supprimer";
"edit" = "Modifier";
"done" = "Terminé";
"close" = "Fermer";
"back" = "Retour";

// MARK: - Mixer
"mixer_title" = "Mixeur";
"active_sounds_count" = "%d sons actifs";
"no_active_sounds" = "Aucun son actif";
"no_active_sounds_desc" = "Retournez à la page principale pour sélectionner des sons et commencer à mixer";
"save_mix" = "Enregistrer le mix";
"save_as_new_mix" = "Enregistrer comme nouveau mix";
"update_current_mix" = "Enregistrer";
"update_and_add_sounds" = "Mettre à jour et ajouter des sons";
"update_mix" = "Mettre à jour le mix";
"update_mix_prompt" = "Mettre à jour ce mix avec les paramètres actuels ?";
"saved_mixes" = "Mix enregistrés";
"mix_name" = "Nom du mix";
"mix_description" = "Description";
"mix_name_placeholder" = "Nom du mix";
"mix_description_placeholder" = "Description (optionnelle)";
"mix_name_prompt" = "Donnez un nom à votre mix actuel";
"unnamed_mix" = "Mix sans nom";

// MARK: - Favorites
"favorites_title" = "Mes Favoris";
"favorites_subtitle" = "Mix enregistrés";
"favorites_count" = "favoris";
"no_favorites" = "Aucun favori";
"no_favorites_desc" = "Enregistrez vos combinaisons de sons préférées dans le mixeur";
"delete_favorite" = "Supprimer le favori";
"delete_favorite_message" = "Supprimer ce mix favori ?";
"mix_sounds_count" = "%d sons";
"mix_combination" = "Combinaison de mix";

// MARK: - Settings
"settings_title" = "Paramètres";
"settings_subtitle" = "Personnalisez votre application";
"appearance_section" = "Apparence";
"language_section" = "Langue";
"playback_section" = "Lecture";
"about_section" = "À propos";

// Apparence
"theme_color" = "Couleur du thème";
"dark_mode" = "Mode sombre";
"follow_system" = "Suivre le système";

// Langue
"app_language" = "Langue de l'application";
"system_language_settings" = "Paramètres système";

// Lecture
"auto_play" = "Lecture automatique";
"auto_play_desc" = "Reprendre le dernier mix à l'ouverture de l'application";
"background_play" = "Lecture en arrière-plan";
"background_play_desc" = "Continuer la lecture en utilisant d'autres applications";
"lock_screen_control" = "Contrôle écran verrouillé";
"lock_screen_control_desc" = "Contrôler la lecture depuis l'écran verrouillé et le centre de contrôle";

// Lock Screen Control VIP
"lock_screen_control_title" = "Contrôle écran verrouillé";
"lock_screen_control_vip_desc" = "Passez VIP pour contrôler la lecture depuis l'écran verrouillé et le centre de contrôle";
"lock_screen_control_feature" = "Contrôle écran verrouillé";
"lock_screen_control_feature_desc" = "Contrôler la lecture depuis l'écran verrouillé";
"lock_screen_control_benefit1" = "Contrôle sans déverrouiller l'appareil";
"lock_screen_control_benefit2" = "Accès rapide depuis le centre de contrôle";
"lock_screen_control_benefit3" = "Intégration transparente avec les contrôles média iOS";

// Messages de lecture automatique
"auto_play_enabled" = "Lecture automatique activée";
"auto_play_disabled" = "Lecture automatique désactivée";
"no_last_mix_found" = "Dernier mix introuvable";
"last_mix_loaded" = "Dernier mix chargé";
"auto_play_mix_name" = "Mix lecture auto";
"auto_play_mix_desc" = "Mix enregistré pour la lecture automatique";

// Messages de lecture en arrière-plan
"background_play_enabled" = "Lecture en arrière-plan activée";
"background_play_disabled" = "Lecture en arrière-plan désactivée";

// À propos
"version" = "Version";
"feedback" = "Retour";
"feedback_desc" = "Envoyer des retours et suggestions";
"rate_app" = "Noter";
"rate_app_desc" = "Noter sur l'App Store";

// MARK: - Timer
"timer" = "Minuteur";
"timer_remaining" = "Restant %@";
"set_timer" = "Définir le minuteur";
"timer_minutes" = "minutes";
"timer_hours" = "heures";

// MARK: - Alerts & Messages
"error" = "Erreur";
"success" = "Succès";
"loading" = "Chargement...";
"saved_successfully" = "Enregistré avec succès";
"deleted_successfully" = "Supprimé avec succès";

// MARK: - Accessibility
"play_button" = "Bouton lecture";
"pause_button" = "Bouton pause";
"volume_slider" = "Curseur de volume";
"close_button" = "Bouton fermer";

// MARK: - VIP Subscription
// Titres des fonctionnalités VIP
"vip_sound_title" = "Sons VIP exclusifs";
"mix_limit_title" = "Mix illimités";
"save_limit_title" = "Favoris illimités";
"vip_theme_title" = "Thèmes VIP exclusifs";
"long_timer_title" = "Minuteur long";

// Descriptions des fonctionnalités VIP
"vip_sound_desc" = "Ce son nécessite un abonnement VIP. Passez VIP pour débloquer tous les sons exclusifs haute qualité !";
"mix_limit_desc" = "Les utilisateurs gratuits peuvent jouer seulement %d sons simultanément. Passez VIP pour des mix illimités !";
"save_limit_desc" = "Les utilisateurs gratuits peuvent sauvegarder seulement %d favoris. Passez VIP pour des favoris illimités !";
"vip_theme_desc" = "Ce thème nécessite un abonnement VIP. Passez VIP pour débloquer tous les beaux thèmes !";
"long_timer_desc" = "Les utilisateurs gratuits peuvent définir seulement 30 minutes maximum. Passez VIP pour des minuteurs plus longs !";

// Avantages VIP
"upgrade_will_get" = "En passant VIP, vous obtiendrez :";
"all_vip_benefits" = "Tous les avantages VIP";

// Avantages des sons VIP
"vip_sound_benefit_1" = "Débloquez tous les sons VIP exclusifs";
"vip_sound_benefit_2" = "Expérience audio haute qualité";
"vip_sound_benefit_3" = "Contenu sonore exclusif";

// Avantages des mix illimités
"mix_limit_benefit_1" = "Ajoutez des sons sans limite à vos mix";
"mix_limit_benefit_2" = "Créez des combinaisons sonores complexes";
"mix_limit_benefit_3" = "Expérience de mixage professionnelle";

// Avantages des favoris illimités
"save_limit_benefit_1" = "Sauvegardez un nombre illimité de favoris";
"save_limit_benefit_2" = "Créez votre bibliothèque sonore personnelle";
"save_limit_benefit_3" = "Accédez à tout moment à vos mix préférés";

// Avantages des thèmes VIP
"vip_theme_benefit_1" = "Débloquez tous les beaux thèmes";
"vip_theme_benefit_2" = "Expérience d'interface personnalisée";
"vip_theme_benefit_3" = "Design visuel exclusif";

// Avantages du minuteur long
"long_timer_benefit_1" = "Définissez des minuteurs de plus de 30 minutes";
"long_timer_benefit_2" = "Prolongez votre temps de relaxation";
"long_timer_benefit_3" = "Idéal pour le sommeil et la méditation";

// Liste des fonctionnalités VIP (pour la vue complète)
"vip_sound_feature" = "Sons VIP exclusifs";
"vip_sound_feature_desc" = "Débloquez des sons exclusifs haute qualité";
"mix_limit_feature" = "Mix illimités";
"mix_limit_feature_desc" = "Ajoutez des sons sans limite";
"save_limit_feature" = "Favoris illimités";
"save_limit_feature_desc" = "Sauvegardez un nombre illimité de favoris";
"vip_theme_feature" = "Thèmes VIP exclusifs";
"vip_theme_feature_desc" = "Utilisez tous les beaux thèmes";
"long_timer_feature" = "Minuteur long";
"long_timer_feature_desc" = "Minuteur de plus de 30 minutes";

// Boutons d'action
"upgrade_to_vip" = "Passer VIP";
"later_button" = "Plus tard";
"tap_to_upgrade" = "Taper pour passer VIP";

// MARK: - Theme Names
"theme_ocean_breeze" = "Brise océane";
"theme_sunset_glow" = "Lueur du couchant";
"theme_forest_mist" = "Brumes forestières";
"theme_night_sky" = "Ciel étoilé";
"theme_lavender_dream" = "Rêve de lavande";
"theme_fire_ember" = "Braises ardentes";

// MARK: - Theme Selector
"select_theme" = "Sélectionner pour utiliser";
"current_theme" = "Actuel : %@";
"theme_in_use" = "En cours d'utilisation";
"previous_theme" = "Précédent";
"next_theme" = "Suivant";

// Confirmation de changement de thème
"confirm_theme_change" = "Confirmer le changement de thème";
"confirm_theme_change_message" = "Passer au thème \"%@\" ?";
"confirm" = "Confirmer";

// Confirmation de suppression de favori
"delete_favorite_confirm" = "Supprimer le mix favori";
"delete_favorite_message" = "Supprimer le dernier son supprimera aussi le mix \"%@\". Continuer ?";
"delete_favorite" = "Supprimer le favori";

// MARK: - Timer
"timer" = "Minuteur";
"time_remaining" = "Temps restant";
"set_timer" = "Définir le minuteur";
"timer_description" = "Choisissez la durée de lecture, s'arrête automatiquement";
"quick_select" = "Sélection rapide";
"custom_time" = "Temps personnalisé";
"hours" = "heures";
"minutes" = "minutes";
"seconds" = "secondes";
"start_timer" = "Démarrer le minuteur";
"cancel_timer" = "Annuler le minuteur";
"elapsed_time" = "Lecture depuis %@";

// Durées du minuteur
"timer_15min" = "15 minutes";
"timer_30min" = "30 minutes";
"timer_45min" = "45 minutes";
"timer_1hour" = "1 heure";
"timer_1_5hour" = "1.5 heures";
"timer_2hour" = "2 heures";

// Info de lecture en cours
"playing_count" = "%d/%d sons en lecture";
"active_sounds_count" = "%d sons actifs";
"app_subtitle" = "Mixeur d'ambiances sonores";

// MARK: - Subscription Plans
"monthly_subscription" = "Abonnement mensuel";
"yearly_subscription" = "Abonnement annuel";
"lifetime_purchase" = "Achat à vie";
"popular_badge" = "Populaire";
"best_value_badge" = "Meilleur rapport";
"one_time_purchase" = "Achat unique";
"cancel_anytime" = "Annulable à tout moment";
"save_over_30_percent" = "Économisez plus de 30% vs mensuel";
"lifetime_description" = "Achetez une fois, profitez de toutes les fonctionnalités VIP à vie";
"choose_subscription_plan" = "Choisissez un abonnement";
"loading_subscription_options" = "Chargement des options...";
"restore_purchases" = "Restaurer les achats";
"terms_of_service" = "Conditions d'utilisation";
"privacy_policy" = "Politique de confidentialité";
"subscription_success" = "Abonnement réussi ! Merci de votre soutien !";
"purchase_failed" = "Échec d'achat : %@";
"restore_success" = "Achats restaurés !";
"no_purchases_to_restore" = "Aucun achat à restaurer";
"processing" = "Traitement...";

// MARK: - Subscription Interface
"upgrade_to_vip_title" = "Passer VIP";
"upgrade_subtitle" = "Débloquez toutes les fonctionnalités premium et l'expérience complète de DIY Noise";
"vip_membership_benefits" = "Avantages VIP";
"preview_mode_notice" = "Mode prévisualisation - Configurez vos produits App Store Connect";

// Période d'abonnement
"daily" = "Quotidien";
"weekly" = "Hebdomadaire";
"monthly" = "Mensuel";
"yearly" = "Annuel";

// Fonctionnalités VIP
"vip_exclusive_sounds" = "Sons VIP exclusifs";
"vip_exclusive_sounds_desc" = "Débloquez tous les sons exclusifs haute qualité";
"unlimited_mixing" = "Mix illimités";
"unlimited_mixing_desc" = "Ajoutez des sons sans limite à vos mix";
"unlimited_favorites" = "Favoris illimités";
"unlimited_favorites_desc" = "Sauvegardez un nombre illimité de mix";
"vip_exclusive_themes" = "Thèmes VIP exclusifs";
"vip_exclusive_themes_desc" = "Utilisez tous les beaux thèmes d'interface";
"lock_screen_control_feature" = "Contrôle écran verrouillé";
"lock_screen_control_feature_desc" = "Contrôlez la lecture depuis l'écran verrouillé";
"extended_timer" = "Minuteur étendu";
"extended_timer_desc" = "Minuteur de plus de 30 minutes";

// Alerte
"alert_title" = "Alerte";
"ok_button" = "OK";

import SwiftUI

@main
struct DIYNoiseApp: App {
    @StateObject private var audioManager = AudioManager.shared
    @StateObject private var settingsManager = SettingsManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
                    handleAppWillResignActive()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                    handleAppDidBecomeActive()
                }
        }
    }

    private func handleAppWillResignActive() {

        // 检查后台播放权限
        let hasBackgroundPlayPermission = settingsManager.backgroundPlay && settingsManager.canToggleBackgroundPlay()

        if hasBackgroundPlayPermission && audioManager.isPlaying {

            // 确保音频会话配置正确
            audioManager.configureAudioSessionForCurrentSettings()

            // 强制更新 Now Playing 信息
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.audioManager.updateNowPlayingInfo()
            }

        } else if !hasBackgroundPlayPermission && audioManager.isPlaying {

            // 记录当前播放状态，以便应用激活时恢复
            let playingStates = audioManager.activeSounds.mapValues { $0.isPlaying }
            if let data = try? JSONEncoder().encode(playingStates) {
                UserDefaults.standard.set(data, forKey: "pausedSoundsForBackground")
            }
            UserDefaults.standard.set(true, forKey: "wasPausedForBackground")

            // 暂停所有音频，因为用户没有开启后台播放
            audioManager.pauseAllSounds()
        } else {
        }
    }

    private func handleAppDidBecomeActive() {

        // 应用激活时确保音频会话配置正确
        audioManager.configureAudioSessionForCurrentSettings()

        // 检查当前主题权限，VIP 过期时自动切换
        ThemeManager.shared.validateCurrentThemePermission()

        // 检查是否因为后台播放权限不足而暂停了音频
        let wasPausedForBackground = UserDefaults.standard.bool(forKey: "wasPausedForBackground")
        if wasPausedForBackground {

            // 清除暂停标记
            UserDefaults.standard.removeObject(forKey: "wasPausedForBackground")

            // 恢复播放状态（无论是否有后台播放权限，前台都应该能播放）
            if let data = UserDefaults.standard.data(forKey: "pausedSoundsForBackground"),
               let playingStates = try? JSONDecoder().decode([UUID: Bool].self, from: data) {


                // 恢复之前正在播放的声音
                var restoredCount = 0
                for (soundId, wasPlaying) in playingStates {
                    if wasPlaying, let player = audioManager.activeSounds[soundId] {
                        player.play()
                        restoredCount += 1
                    }
                }

                // 更新播放状态
                audioManager.updatePlayingStateFromPlayers()

                // 清除保存的播放状态
                UserDefaults.standard.removeObject(forKey: "pausedSoundsForBackground")
            } else {
                // 如果没有保存的播放状态，就恢复所有声音
                audioManager.resumeAllSounds()
            }
        }
    }
}
